// COVID-19 App Achievement Content
export const covidAppContent = {
    id: "covid-app",
    title: "🎖️ Tham gia Cuộc thi Khoa học Kỹ thuật cấp Quốc gia năm học 2021–2022",
    date: "27/03/2022",
    excerpt: "Dự án CouNa - Ứng dụng hỗ trợ kết nối Bệnh nhân, <PERSON><PERSON>c sĩ và Tình nguyện viên trong mùa dịch Covid-19 đạt Giải Nhất cấp tỉnh và tham gia cuộc thi cấp Quốc gia.",
    images: ["../assets/images/achievements/covid-app-award.jpg"],
    content: `
        <div class="project-info-section">
            <h2>📋 Thông tin dự án</h2>
            <div class="info-grid">
                <div class="info-item"><strong>👤 Họ và tên:</strong> <PERSON><PERSON></div>
                <div class="info-item"><strong>🏫 Trường:</strong> THPT <PERSON><PERSON>ê<PERSON>ễn Tất Thành</div>
                <div class="info-item"><strong>📍 Tỉnh/Thành phố:</strong> Kon Tum</div>
                <div class="info-item"><strong>📅 Thời gian:</strong> Năm học 2021–2022</div>
                <div class="info-item"><strong>📘 Lĩnh vực:</strong> Hệ thống nhúng (Embedded Systems)</div>
                <div class="info-item"><strong>📱 Tên dự án:</strong> CouNa – Ứng dụng hỗ trợ kết nối Bệnh nhân, Bác sĩ và Tình nguyện viên trong mùa dịch Covid-19</div>
                <div class="info-item"><strong>🥇 Giải thưởng:</strong> Giải Nhất - Cấp Tỉnh và được suất tham dự Cuộc thi Khoa học Kỹ thuật cấp Quốc gia</div>
            </div>
        </div>

        <h2>🏆 Mô tả thành tích</h2>
        <p>Tôi đã tham gia thành công Cuộc thi Khoa học Kỹ thuật cấp Quốc gia năm học 2021–2022 – một sân chơi trí tuệ cấp quốc gia dành cho học sinh trung học, với dự án thuộc lĩnh vực Hệ thống nhúng. Dự án mang tên CouNa, hướng tới mục tiêu hỗ trợ cộng đồng trong thời kỳ dịch bệnh Covid-19 diễn biến phức tạp.</p>

        <h2>🔧 Giới thiệu dự án CouNa</h2>
        <p>CouNa là một ứng dụng di động được phát triển trên nền tảng Android Studio, kết hợp với Firebase để quản lý dữ liệu tài khoản của ba nhóm đối tượng: Bác sĩ, Bệnh nhân và Tình nguyện viên.</p>

        <div class="feature-list">
            <div class="feature-item">
                <strong>🏥 Bệnh nhân:</strong> có thể khai báo các triệu chứng, tình trạng sức khỏe của mình trực tiếp trên ứng dụng.
            </div>
            <div class="feature-item">
                <strong>👨‍⚕️ Bác sĩ:</strong> có thể truy cập, theo dõi và đánh giá triệu chứng của từng bệnh nhân để kịp thời hỗ trợ.
            </div>
            <div class="feature-item">
                <strong>🤝 Tình nguyện viên:</strong> có thể hỗ trợ vận chuyển thuốc men, nhu yếu phẩm hoặc hỗ trợ y tế tại chỗ cho bệnh nhân có nhu cầu.
            </div>
        </div>

        <h2>💡 Mục đích</h2>
        <p>Ứng dụng nhằm giải quyết bài toán quá tải hệ thống y tế trong giai đoạn dịch bệnh bùng phát mạnh, giúp:</p>
        <ul>
            <li>Giảm tải cho các y bác sĩ bằng cách theo dõi triệu chứng từ xa.</li>
            <li>Kết nối kịp thời các ca bệnh nặng với đội ngũ y tế và tình nguyện viên.</li>
            <li>Tăng tính chủ động cho bệnh nhân trong việc tự đánh giá tình trạng bản thân.</li>
        </ul>

        <div class="certificate-info">
            <h3>📅 Ngày cấp giấy chứng nhận: 27/03/2022</h3>
        </div>
    `
};
