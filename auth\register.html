<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON></title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        
        .page-header {
            text-align: center;
            margin: 120px 0 30px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .register-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 50px;
            align-items: stretch;
        }
        
        .register-form-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .register-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
        }
        
        .form-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .form-header h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #4285F4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .register-btn {
            background: linear-gradient(to right, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
        }
        
        .rules-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 600px;
        }
        
        .rules-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #ff7aa8, #4285F4);
        }
        
        .rules-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .rules-header {
            flex-shrink: 0;
            margin-bottom: 15px;
        }

        .rules-header h2 {
            color: #ff7aa8;
            font-size: 1.8rem;
            margin-bottom: 0;
        }
        
        .rules-content {
            flex: 1;
            max-height: 900px;
            overflow-y: auto;
            padding-right: 15px;
            padding-bottom: 20px;
        }
        
        .rules-content::-webkit-scrollbar {
            width: 8px;
        }

        .rules-content::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 10px;
            margin: 5px 0;
        }

        .rules-content::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .rules-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #3367D6, #e55a8a);
            transform: scale(1.1);
        }
        
        .rules-content h2 {
            color: #4285F4;
            font-size: 1.4rem;
            margin: 0 0 15px;
            text-align: center;
        }

        .rules-content h3 {
            color: #4285F4;
            font-size: 1.1rem;
            margin: 20px 0 10px;
        }
        
        .rules-content h3:first-child {
            margin-top: 0;
        }
        
        .rules-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .rules-content ol {
            padding-left: 20px;
            margin-bottom: 15px;
        }
        
        .rules-content li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .rules-content li ol {
            list-style-type: lower-alpha;
            margin: 10px 0;
        }
        
        .contact-info {
            margin-top: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        
        .contact-info h2 {
            color: #4285F4;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .contact-methods {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        
        .contact-method {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            padding: 20px;
            border-radius: 10px;
            background-color: #f8f9fa;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .contact-method i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #ff7aa8;
        }
        
        .contact-method h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
            color: #333;
        }
        
        .contact-method p,
        .contact-method a {
            color: #666;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .contact-method a:hover {
            color: #4285F4;
        }
        
        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .agreement-checkbox input {
            margin-top: 5px;
            margin-right: 10px;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }

        .error-message {
            background-color: rgba(255, 77, 77, 0.1);
            color: #ff4d4d;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading i {
            color: #4285F4;
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .register-container {
                flex-direction: column;
            }
            
            .contact-methods {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-method {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* Hiệu ứng animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .register-form-container, .rules-container, .contact-info {
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        .rules-container {
            animation-delay: 0.2s;
        }
        
        .contact-info {
            animation-delay: 0.4s;
            opacity: 0;
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }



        @keyframes gradientShift {
            0%, 100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
            25% {
                opacity: 0.8;
                transform: scale(1.05) rotate(1deg);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1) rotate(0deg);
            }
            75% {
                opacity: 0.8;
                transform: scale(1.05) rotate(-1deg);
            }
        }

        .course-intro {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            max-width: 1300px;
            margin: 0 auto;
            padding: 100px 30px;
            position: relative;
            z-index: 2;
        }

        .course-content {
            z-index: 3;
            position: relative;
        }

        .course-header {
            margin-bottom: 40px;
            text-align: center;
        }

        .course-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .course-image {
            width: 140px;
            height: 140px;
            border-radius: 25px;
            object-fit: cover;
            margin: 0 auto 25px;
            box-shadow:
                0 15px 40px rgba(0, 0, 0, 0.3),
                0 5px 15px rgba(0, 0, 0, 0.2);
            border: 4px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: block;
        }

        .course-image:hover {
            transform: scale(1.08) translateY(-5px);
            box-shadow:
                0 20px 50px rgba(0, 0, 0, 0.4),
                0 8px 25px rgba(0, 0, 0, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .course-header h2 {
            font-size: 3.2rem;
            font-weight: 800;
            margin-bottom: 25px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            line-height: 1.1;
            background: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.9));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            letter-spacing: -1px;
        }

        .course-header h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.6));
            border-radius: 2px;
            box-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .course-description {
            font-size: 1.3rem;
            opacity: 0.95;
            line-height: 1.7;
            margin-bottom: 35px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            font-weight: 300;
            text-align: center;
        }

        .course-features {
            display: flex;
            flex-direction: column;
            gap: 18px;
            margin-bottom: 45px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 18px;
            font-size: 1.15rem;
            background: rgba(255, 255, 255, 0.15);
            padding: 18px 25px;
            border-radius: 15px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(10px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .feature-item i {
            font-size: 1.4rem;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.9);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .register-course-btn {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.4);
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(15px);
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .register-course-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .register-course-btn:hover::before {
            left: 100%;
        }

        .register-course-btn:hover {
            background: rgba(255, 255, 255, 0.35);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-4px) scale(1.02);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }







        /* Final Registration Section */
        .final-registration-section {
            background: linear-gradient(135deg, #7209b7 0%, #533483 25%, #0f3460 50%, #16213e 75%, #1a1a2e 100%);
            color: white;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .final-registration-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(114, 9, 183, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(83, 52, 131, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(15, 52, 96, 0.1) 0%, transparent 40%),
                radial-gradient(circle at 60% 20%, rgba(26, 26, 46, 0.1) 0%, transparent 30%);
            animation: gradientShift 12s ease-in-out infinite;
        }

        .final-registration-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 2px,
                    rgba(255, 255, 255, 0.03) 2px,
                    rgba(255, 255, 255, 0.03) 4px
                );
            opacity: 0.4;
        }

        .registration-content {
            max-width: 900px;
            margin: 0 auto;
            position: relative;
            z-index: 3;
            padding: 0 20px;
        }

        .registration-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .registration-header h2 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 25px;
            background: linear-gradient(45deg, #ffffff, rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            position: relative;
            letter-spacing: -1px;
        }

        .registration-header h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.6));
            border-radius: 2px;
            box-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .registration-header p {
            font-size: 1.4rem;
            opacity: 0.95;
            max-width: 650px;
            margin: 0 auto;
            line-height: 1.6;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .registration-form-container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            padding: 60px;
            border: 2px solid rgba(255, 255, 255, 0.25);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .registration-form-container:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.15);
        }

        .registration-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 30px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: white;
            font-size: 1.1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 20px 25px;
            border: 2px solid rgba(255, 255, 255, 0.25);
            border-radius: 18px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.12);
            color: white;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;
            font-weight: 400;
            backdrop-filter: blur(10px);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
            font-weight: 300;
        }

        .form-group select option {
            color: #333;
            background: white;
            font-weight: 400;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
            box-shadow:
                0 0 30px rgba(255, 255, 255, 0.2),
                0 10px 30px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform: translateY(-3px);
        }

        .form-group input:hover,
        .form-group select:hover {
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .final-submit-btn {
            background: linear-gradient(135deg, #ff6b9d, #c471ed, #12c2e9);
            color: white;
            border: none;
            padding: 22px 60px;
            border-radius: 50px;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 40px auto 0;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.2),
                0 8px 25px rgba(255, 107, 157, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-transform: uppercase;
            letter-spacing: 1.5px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .final-submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .final-submit-btn:hover::before {
            left: 100%;
        }

        .final-submit-btn:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 12px 35px rgba(255, 107, 157, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .final-submit-btn:active {
            transform: translateY(-3px) scale(1.01);
        }

        .final-submit-btn i {
            font-size: 1.2rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }



        /* Courses Section */
        .courses-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            position: relative;
            overflow: hidden;
            color: white;
        }

        .courses-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(26, 26, 46, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(15, 52, 96, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(83, 52, 131, 0.1) 0%, transparent 50%);
            animation: backgroundMove 30s linear infinite;
        }

        .courses-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255, 255, 255, 0.02) 2px, rgba(255, 255, 255, 0.02) 4px),
                repeating-linear-gradient(-45deg, transparent, transparent 3px, rgba(255, 255, 255, 0.01) 3px, rgba(255, 255, 255, 0.01) 6px);
            background-size: 80px 80px, 50px 50px;
            animation: backgroundMove 25s linear infinite reverse;
            opacity: 0.6;
        }

        .courses-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .courses-title {
            font-size: 2.8rem;
            margin-bottom: 20px;
            color: white;
            position: relative;
            display: inline-block;
        }

        .courses-title .gradient-text {
            background: linear-gradient(135deg, #ffd700, #ff8c00, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-decoration {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .decoration-line {
            width: 50px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ffd700, transparent);
        }

        .title-decoration i {
            color: #ffd700;
            font-size: 1.5rem;
        }

        .courses-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 600px;
            margin: 0 auto;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .course-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .course-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .course-card:hover::before {
            opacity: 1;
        }

        .course-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .course-icon {
            font-size: 3rem;
            color: #ffd700;
        }

        .course-badge {
            background: linear-gradient(135deg, #ff6b35, #ff8c00);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .course-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: white;
            font-weight: 600;
        }

        .course-details {
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }

        .detail-item i {
            color: #ffd700;
            width: 20px;
        }

        .trial-offer {
            background: rgba(255, 215, 0, 0.2);
            border-radius: 10px;
            padding: 10px;
            margin-top: 15px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .trial-offer i {
            color: #ffd700;
        }

        .course-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }

        .feature-tag {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        /* Background Elements */
        .courses-bg-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .courses-bg-elements .bg-element {
            position: absolute;
            font-size: 2rem;
            opacity: 0.15;
            animation: float 6s ease-in-out infinite;
            color: rgba(255, 255, 255, 0.3);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        .courses-bg-elements .book {
            top: 10%;
            left: 5%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .graduation {
            top: 20%;
            right: 10%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .lightbulb {
            bottom: 30%;
            left: 8%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .computer {
            bottom: 15%;
            right: 15%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .science {
            top: 50%;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: var(--delay);
        }

        @keyframes backgroundMove {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .courses-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 0 15px;
            }

            .courses-title {
                font-size: 2.2rem;
            }

            .courses-subtitle {
                font-size: 1.1rem;
            }

            .course-card {
                margin: 0 10px;
            }

            .course-intro {
                grid-template-columns: 1fr;
                gap: 50px;
                padding: 60px 20px;
            }

            .course-header h2 {
                font-size: 2.5rem;
            }

            .course-description {
                font-size: 1.1rem;
            }

            .feature-item {
                font-size: 1rem;
                padding: 15px 20px;
            }

            .register-course-btn {
                font-size: 1.1rem;
                padding: 16px 35px;
            }

            .course-image {
                width: 120px;
                height: 120px;
            }





            .final-registration-section {
                padding: 60px 20px;
            }

            .registration-header h2 {
                font-size: 2rem;
            }

            .registration-form-container {
                padding: 30px 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .final-submit-btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html" class="active">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Courses Section -->
    <section class="courses-section" id="courses">
        <div class="container">
            <div class="courses-header" data-aos="fade-up">
                <h2 class="courses-title">
                    <span class="gradient-text">Khóa Học Của Chúng Tôi</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-graduation-cap"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="courses-subtitle">Chọn khóa học phù hợp với bạn và bắt đầu hành trình học tập</p>
            </div>

            <div class="courses-grid">
                <!-- Python-AI Course -->
                <div class="course-card python-course" data-aos="fade-up" data-aos-delay="100">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fab fa-python"></i>
                        </div>
                        <div class="course-badge">HOT</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Python - AI từ Cơ Bản đến Nâng Cao</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>250,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🐍 Python</span>
                            <span class="feature-tag">🤖 AI/ML</span>
                            <span class="feature-tag">💻 Coding</span>
                        </div>
                    </div>
                </div>

                <!-- Scratch Course -->
                <div class="course-card scratch-course" data-aos="fade-up" data-aos-delay="200">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div class="course-badge">NEW</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Scratch - Tin Học Cơ Bản</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh Tiểu học</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>300,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🎨 Scratch</span>
                            <span class="feature-tag">📊 Excel</span>
                            <span class="feature-tag">📝 Word</span>
                            <span class="feature-tag">🖼️ Canva</span>
                        </div>
                    </div>
                </div>

                <!-- STEM Course -->
                <div class="course-card stem-course" data-aos="fade-up" data-aos-delay="300">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <div class="course-badge">PRO</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Hỗ Trợ Nghiên Cứu KHKT - STEM</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>350,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="detail-item special">
                                <i class="fas fa-star"></i>
                                <span>4 buổi code + 4 buổi slides</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🔬 KHKT</span>
                            <span class="feature-tag">📊 STEM</span>
                            <span class="feature-tag">📋 Báo cáo</span>
                            <span class="feature-tag">🏆 Dự án</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="courses-bg-elements">
            <div class="bg-element book" style="--delay: 0s;">📚</div>
            <div class="bg-element graduation" style="--delay: 1s;">🎓</div>
            <div class="bg-element lightbulb" style="--delay: 2s;">💡</div>
            <div class="bg-element computer" style="--delay: 0.5s;">💻</div>
            <div class="bg-element science" style="--delay: 1.5s;">🔬</div>
        </div>
    </section>

    <!-- Final Registration Section -->
    <section class="final-registration-section" id="finalRegistration">
        <div class="container">
            <div class="registration-content">
                <div class="registration-header">
                    <h2>Liên Hệ Tư Vấn</h2>
                    <p>Điền thông tin để chúng tôi tư vấn và hỗ trợ bạn chọn khóa học phù hợp nhất</p>
                </div>

                <div class="registration-form-container">
                    <form id="finalRegistrationForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="finalStudentName">Tên Học Viên <span class="required">*</span></label>
                                <input type="text" id="finalStudentName" name="studentName" required>
                            </div>

                            <div class="form-group">
                                <label for="finalBirthDate">Ngày Sinh <span class="required">*</span></label>
                                <input type="date" id="finalBirthDate" name="birthDate" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="finalParentName">Tên Phụ Huynh <span class="required">*</span></label>
                                <input type="text" id="finalParentName" name="parentName" required>
                            </div>

                            <div class="form-group">
                                <label for="finalParentPhone">Số Điện Thoại Phụ Huynh <span class="required">*</span></label>
                                <input type="tel" id="finalParentPhone" name="parentPhone" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="finalCourseSelect">Chọn Khóa Học <span class="required">*</span></label>
                            <select id="finalCourseSelect" name="course" required>
                                <option value="">-- Chọn khóa học --</option>
                                <option value="python">Python - AI từ Cơ Bản đến Nâng Cao (250,000 VNĐ/tháng)</option>
                                <option value="scratch">Scratch - Tin Học Cơ Bản (300,000 VNĐ/tháng)</option>
                                <option value="stem">Hỗ Trợ Nghiên Cứu KHKT - STEM (350,000 VNĐ/tháng)</option>
                            </select>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="final-submit-btn">
                                <i class="fas fa-paper-plane"></i>
                                Gửi Đăng Ký
                            </button>
                        </div>
                    </form>

                    <div class="success-message" id="finalSuccessMessage" style="display:none;">
                        <i class="fas fa-check-circle"></i>
                        <h3>Đăng ký thành công!</h3>
                        <p>Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất để xác nhận và cung cấp thông tin chi tiết về khóa học.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getFirestore, collection, addDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Course titles mapping
        const courseTitles = {
            'python': 'Python - AI từ Cơ Bản đến Nâng Cao',
            'scratch': 'Scratch - Tin Học Cơ Bản',
            'stem': 'Hỗ Trợ Nghiên Cứu KHKT - STEM'
        };



        // Scroll to registration section
        window.scrollToRegistration = function(courseType) {
            const finalRegistration = document.getElementById('finalRegistration');
            const courseSelect = document.getElementById('finalCourseSelect');

            // Set the course in the select
            courseSelect.value = courseType;

            // Smooth scroll to registration section
            finalRegistration.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Add highlight effect
            finalRegistration.style.animation = 'highlightSection 2s ease-in-out';
            setTimeout(() => {
                finalRegistration.style.animation = '';
            }, 2000);
        };

        // Video control functions
        window.toggleVideo = function() {
            const video = document.getElementById('scratchVideo');
            const playBtn = document.getElementById('playBtn');

            if (video.paused) {
                video.play();
                playBtn.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                video.pause();
                playBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        };

        window.toggleMute = function() {
            const video = document.getElementById('scratchVideo');
            const muteBtn = document.getElementById('muteBtn');

            if (video.muted) {
                video.muted = false;
                muteBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
            } else {
                video.muted = true;
                muteBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
            }
        };

        window.toggleFullscreen = function() {
            const video = document.getElementById('scratchVideo');

            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        };



        // Smooth scrolling for course sections
        function scrollToCourse(courseId) {
            document.getElementById(courseId + '-registration').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Handle final form submission
        const finalForm = document.getElementById('finalRegistrationForm');
        const finalSuccessMessage = document.getElementById('finalSuccessMessage');

        finalForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('finalStudentName').value,
                    birthDate: document.getElementById('finalBirthDate').value,
                    parentName: document.getElementById('finalParentName').value,
                    parentPhone: document.getElementById('finalParentPhone').value,
                    course: document.getElementById('finalCourseSelect').value,
                    courseName: courseTitles[document.getElementById('finalCourseSelect').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending'
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Hide form and show success message
                finalForm.style.display = 'none';
                finalSuccessMessage.style.display = 'block';

                // Reset form after 5 seconds
                setTimeout(() => {
                    finalForm.style.display = 'block';
                    finalSuccessMessage.style.display = 'none';
                    finalForm.reset();
                }, 5000);

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Add highlight animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes highlightSection {
                0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
                50% { box-shadow: 0 0 0 20px rgba(52, 152, 219, 0.3); }
                100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
            }
        `;
        document.head.appendChild(style);

        // Check URL parameters for direct course access
        const urlParams = new URLSearchParams(window.location.search);
        const course = urlParams.get('course');
        if (course && courseTitles[course]) {
            setTimeout(() => {
                scrollToRegistration(course);
            }, 500);
        }
    </script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>