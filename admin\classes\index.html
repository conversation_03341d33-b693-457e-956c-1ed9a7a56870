<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> <PERSON> - VT Academy</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .classes-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .page-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .class-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border: 2px solid #ecf0f1;
            transition: all 0.3s ease;
        }

        .class-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .class-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .class-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .class-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .class-card:nth-child(2) .class-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .class-card:nth-child(3) .class-icon {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .class-card:nth-child(4) .class-icon {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .class-info h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .class-info p {
            margin: 0.5rem 0 0 0;
            color: #7f8c8d;
        }

        .registration-toggle {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #4CAF50;
        }

        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(30px);
        }

        .toggle-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .class-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-open {
            background: #d4edda;
            color: #155724;
        }

        .status-closed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-full {
            background: #fff3cd;
            color: #856404;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .class-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .class-stats {
                grid-template-columns: 1fr 1fr;
            }
            
            .registration-toggle {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../classes/">Lớp Học</a></li>
                    <li><a href="../../achievements/">Thành Tích</a></li>
                    <li><a href="../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../auth/">Tài Khoản</a></li>
                    <li><a href="../" class="active">Quản Trị</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="classes-container">
                <div class="page-header">
                    <h1><i class="fas fa-school"></i> Quản Lý Lớp Học</h1>
                    <a href="../" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>

                <!-- Classes List -->
                <div id="classesContainer">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Đang tải thông tin lớp học...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-brand">
                    <div class="footer-logo">
                        <img src="../../assets/images/logo.jpg" alt="Vthon Logo">
                        <span>Vthon Academy</span>
                    </div>
                    <p>Học, học nữa, học mãi</p>
                    <p class="footer-copyright">© 2025 – All rights reserved</p>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" target="_blank">
                            <i class="fab fa-tiktok"></i>
                        </a>
                        <a href="#" target="_blank">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { 
            getFirestore, 
            doc, 
            getDoc, 
            updateDoc, 
            collection, 
            query, 
            getDocs, 
            where
        } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let classesData = [];

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            if (!user || user.email !== '<EMAIL>') {
                window.location.href = '../../auth/';
                return;
            }

            await loadClasses();
        });

        // Load classes data
        async function loadClasses() {
            try {
                // Get class settings
                const classesQuery = query(collection(db, "classSettings"));
                const classesSnapshot = await getDocs(classesQuery);
                
                classesData = [];
                for (const doc of classesSnapshot.docs) {
                    const classData = doc.data();
                    
                    // Count current students
                    const studentsQuery = query(
                        collection(db, "users"),
                        where("courseClass", "==", doc.id)
                    );
                    const studentsSnapshot = await getDocs(studentsQuery);
                    let currentStudents = 0;
                    
                    studentsSnapshot.forEach((studentDoc) => {
                        const studentData = studentDoc.data();
                        if (studentData.fullName && studentData.email !== '<EMAIL>') {
                            currentStudents++;
                        }
                    });
                    
                    classesData.push({
                        id: doc.id,
                        ...classData,
                        currentStudents
                    });
                }

                displayClasses();
            } catch (error) {
                console.error('Error loading classes:', error);
                showError('Lỗi khi tải thông tin lớp học');
            }
        }

        // Display classes
        function displayClasses() {
            const container = document.getElementById('classesContainer');
            
            if (classesData.length === 0) {
                container.innerHTML = `
                    <div class="no-classes">
                        <i class="fas fa-school"></i>
                        <h3>Chưa có lớp học nào</h3>
                        <p>Thông tin lớp học sẽ hiển thị ở đây</p>
                    </div>
                `;
                return;
            }

            let classesHTML = '';
            
            classesData.forEach(classData => {
                const progressPercentage = (classData.currentStudents / classData.maxStudents) * 100;
                const isFull = classData.currentStudents >= classData.maxStudents;
                
                let statusBadge = '';
                if (isFull) {
                    statusBadge = '<span class="status-badge status-full">Đã đầy</span>';
                } else if (classData.isRegistrationOpen) {
                    statusBadge = '<span class="status-badge status-open">Đang mở</span>';
                } else {
                    statusBadge = '<span class="status-badge status-closed">Đã đóng</span>';
                }

                classesHTML += `
                    <div class="class-card">
                        <div class="class-header">
                            <div class="class-title">
                                <div class="class-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="class-info">
                                    <h3>${classData.className}</h3>
                                    <p>Mã lớp: ${classData.id.toUpperCase()}</p>
                                </div>
                            </div>
                            <div class="registration-toggle">
                                ${statusBadge}
                                <span class="toggle-label">Cho phép đăng ký</span>
                                <div class="toggle-switch ${classData.isRegistrationOpen ? 'active' : ''}" 
                                     onclick="toggleRegistration('${classData.id}')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="class-stats">
                            <div class="stat-item">
                                <div class="stat-value">${classData.currentStudents}</div>
                                <div class="stat-label">Học viên hiện tại</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${classData.maxStudents}</div>
                                <div class="stat-label">Giới hạn tối đa</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${Math.round(progressPercentage)}%</div>
                                <div class="stat-label">Tỷ lệ lấp đầy</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${classData.maxStudents - classData.currentStudents}</div>
                                <div class="stat-label">Chỗ trống</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = classesHTML;
        }

        // Toggle registration status
        window.toggleRegistration = async function(classId) {
            try {
                const classData = classesData.find(c => c.id === classId);
                if (!classData) return;

                const newStatus = !classData.isRegistrationOpen;
                
                // Update in Firestore
                await updateDoc(doc(db, "classSettings", classId), {
                    isRegistrationOpen: newStatus,
                    updatedAt: new Date().toISOString()
                });

                // Update local data
                classData.isRegistrationOpen = newStatus;
                
                // Refresh display
                displayClasses();
                
                const statusText = newStatus ? 'mở' : 'đóng';
                showSuccess(`Đã ${statusText} đăng ký cho lớp ${classData.className}`);
                
            } catch (error) {
                console.error('Error toggling registration:', error);
                showError('Lỗi khi cập nhật trạng thái đăng ký');
            }
        };

        // Utility functions
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(errorDiv, container.firstChild);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(successDiv, container.firstChild);
            
            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
