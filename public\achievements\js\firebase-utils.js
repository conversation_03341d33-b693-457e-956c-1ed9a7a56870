// Firebase utilities for achievements
import { doc, getDoc, setDoc, updateDoc, increment, arrayUnion, arrayRemove } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

// Get achievement statistics from Firebase
export async function getAchievementStats(db, achievementId, currentUser = null) {
    try {
        const docRef = doc(db, "achievements", achievementId);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            return docSnap.data();
        } else {
            // Initialize if doesn't exist - but only if user is authenticated
            const initialStats = {
                views: 0,
                likes: 0,
                likedBy: [],
                createdAt: new Date().toISOString()
            };
            
            // Only try to create document if user is authenticated
            if (currentUser) {
                try {
                    await setDoc(docRef, initialStats);
                } catch (setError) {
                    console.warn('Could not create achievement stats document:', setError);
                }
            }
            return initialStats;
        }
    } catch (error) {
        console.error('Error getting achievement stats:', error);
        // Return default stats instead of failing
        return { views: 0, likes: 0, likedBy: [] };
    }
}

// Toggle like for achievement
export async function toggleLike(db, achievementId, currentUser) {
    if (!currentUser) {
        throw new Error('User must be authenticated to like achievements');
    }

    try {
        const docRef = doc(db, "achievements", achievementId);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            const data = docSnap.data();
            const likedBy = data.likedBy || [];
            const isLiked = likedBy.includes(currentUser.uid);

            if (isLiked) {
                // Unlike
                await updateDoc(docRef, {
                    likes: increment(-1),
                    likedBy: arrayRemove(currentUser.uid)
                });
                return false; // Not liked anymore
            } else {
                // Like
                await updateDoc(docRef, {
                    likes: increment(1),
                    likedBy: arrayUnion(currentUser.uid)
                });
                return true; // Now liked
            }
        } else {
            // Create document if it doesn't exist
            await setDoc(docRef, {
                views: 0,
                likes: 1,
                likedBy: [currentUser.uid],
                createdAt: new Date().toISOString()
            });
            return true; // Now liked
        }
    } catch (error) {
        console.error('Error toggling like:', error);
        throw error;
    }
}

// Increment view count
export async function incrementViewCount(db, achievementId) {
    try {
        const docRef = doc(db, "achievements", achievementId);
        
        // Check if document exists first
        const docSnap = await getDoc(docRef);
        if (!docSnap.exists()) {
            // Create document with initial view count
            await setDoc(docRef, {
                views: 1,
                likes: 0,
                likedBy: [],
                lastViewed: new Date().toISOString(),
                createdAt: new Date().toISOString()
            });
        } else {
            // Update existing document
            await updateDoc(docRef, {
                views: increment(1),
                lastViewed: new Date().toISOString()
            });
        }
    } catch (error) {
        console.error('Error incrementing view count:', error);
        // Don't throw error, just log it
    }
}
