// Achievements Manager - Main logic for achievements functionality
import { auth, db } from './firebase-config.js';
import { achievementsData } from '../data/achievements-data.js';
import { getAchievementStats, toggleLike, incrementViewCount } from './firebase-utils.js';
import { createAchievementCard, updateModalContent, openImageFullscreen, showLoading, showError, showEmpty } from './ui-components.js';
import { onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";

class AchievementsManager {
    constructor() {
        this.currentUser = null;
        this.currentAchievementId = null;
        this.container = null;
        this.modal = null;
        
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }

        // Setup Firebase auth listener
        onAuthStateChanged(auth, (user) => {
            this.currentUser = user;
            this.loadAchievements();
        });
    }

    setupEventListeners() {
        this.container = document.getElementById('achievementsContainer');
        this.modal = document.getElementById('achievementModal');

        // Modal event listeners
        const modalLikeBtn = document.getElementById('modalLikeBtn');
        const closeModal = document.querySelector('.close-modal');

        if (modalLikeBtn) {
            modalLikeBtn.addEventListener('click', () => {
                if (this.currentAchievementId) {
                    this.handleLikeClick(this.currentAchievementId, { preventDefault: () => {}, stopPropagation: () => {} });
                }
            });
        }

        if (closeModal) {
            closeModal.addEventListener('click', () => this.closeModal());
        }

        if (this.modal) {
            this.modal.addEventListener('click', (event) => {
                if (event.target === this.modal) {
                    this.closeModal();
                }
            });
        }

        // Setup global event handlers
        window.handleLikeClick = (achievementId, event) => this.handleLikeClick(achievementId, event);
        window.handleReadMore = (achievementId, event) => this.handleReadMore(achievementId, event);
        window.openImageFullscreen = openImageFullscreen;
    }

    async loadAchievements() {
        if (!this.container) return;

        try {
            // Show loading
            showLoading(this.container);

            // Load achievements with stats
            const achievementElements = [];

            for (const [id, achievement] of Object.entries(achievementsData)) {
                const stats = await getAchievementStats(db, id, this.currentUser);
                const element = createAchievementCard(achievement, stats, this.currentUser);
                achievementElements.push(element);
            }

            // Clear container and add achievements
            this.container.innerHTML = '';
            achievementElements.forEach(element => this.container.appendChild(element));

            if (achievementElements.length === 0) {
                showEmpty(this.container);
            }

        } catch (error) {
            console.error('Error loading achievements:', error);
            showError(this.container);
        }
    }

    async handleLikeClick(achievementId, event) {
        event.preventDefault();
        event.stopPropagation();

        if (!this.currentUser) {
            alert('Bạn cần đăng nhập để thích bài viết');
            return;
        }

        try {
            await toggleLike(db, achievementId, this.currentUser);
            // Refresh achievements to update UI
            this.loadAchievements();
            
            // Update modal if it's open
            if (this.currentAchievementId === achievementId && this.modal.classList.contains('active')) {
                await this.updateModalStats(achievementId);
            }
        } catch (error) {
            console.error('Error toggling like:', error);
            alert('Có lỗi xảy ra. Vui lòng thử lại.');
        }
    }

    async handleReadMore(achievementId, event) {
        event.preventDefault();
        await this.openAchievementModal(achievementId);
    }

    async openAchievementModal(achievementId) {
        this.currentAchievementId = achievementId;
        const achievement = achievementsData[achievementId];

        if (!achievement || !this.modal) return;

        // Increment view count
        await incrementViewCount(db, achievementId);

        // Get updated stats
        const stats = await getAchievementStats(db, achievementId, this.currentUser);

        // Update modal content using our UI component
        updateModalContent(achievement, stats, this.currentUser);

        // Show modal
        this.modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    async updateModalStats(achievementId) {
        const achievement = achievementsData[achievementId];
        const stats = await getAchievementStats(db, achievementId, this.currentUser);
        
        // Update only the stats part of the modal
        document.getElementById('modalViewCount').textContent = stats.views || 0;
        document.getElementById('modalLikeCount').textContent = stats.likes || 0;
        
        // Update like button
        const modalLikeBtn = document.getElementById('modalLikeBtn');
        const isLiked = this.currentUser && stats.likedBy && stats.likedBy.includes(this.currentUser.uid);

        modalLikeBtn.className = `modal-like-btn ${isLiked ? 'liked' : ''}`;
        modalLikeBtn.innerHTML = `
            <i class="fas fa-heart"></i>
            <span>${isLiked ? 'Đã thích' : 'Thích bài viết'}</span>
        `;
    }

    closeModal() {
        if (!this.modal) return;
        
        this.modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        this.currentAchievementId = null;

        // Refresh achievements to update stats
        this.loadAchievements();
    }

    // Public method to refresh achievements
    refresh() {
        this.loadAchievements();
    }

    // Public method to get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Public method to check if user is authenticated
    isAuthenticated() {
        return !!this.currentUser;
    }
}

// Export the manager class and create a global instance
export default AchievementsManager;

// Auto-initialize when module is loaded
export const achievementsManager = new AchievementsManager();
