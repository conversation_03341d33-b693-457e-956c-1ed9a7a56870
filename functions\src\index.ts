import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK
admin.initializeApp();

const db = admin.firestore();

/**
 * Cloud Function that triggers when a user is deleted from Firebase Authentication
 * Automatically cleans up all user data from Firestore database
 */
export const cleanupUserData = functions.auth.user().onDelete(async (user) => {
  const uid = user.uid;
  console.log(`User deleted: ${uid}. Starting cleanup process...`);

  try {
    // Start a batch operation for atomic deletion
    const batch = db.batch();

    // 1. Delete user document from users collection
    const userDocRef = db.collection('users').doc(uid);
    batch.delete(userDocRef);
    console.log(`Marked user document for deletion: users/${uid}`);

    // 2. Delete all assignments in the user's assignments subcollection
    const assignmentsRef = db.collection('users').doc(uid).collection('assignments');
    const assignmentsSnapshot = await assignmentsRef.get();
    
    assignmentsSnapshot.forEach((doc) => {
      batch.delete(doc.ref);
      console.log(`Marked assignment for deletion: users/${uid}/assignments/${doc.id}`);
    });

    // 3. Delete any other user-specific subcollections if they exist
    // Add more subcollections here as needed
    const subcollections = ['progress', 'achievements', 'notifications'];
    
    for (const subcollection of subcollections) {
      const subcollectionRef = db.collection('users').doc(uid).collection(subcollection);
      const subcollectionSnapshot = await subcollectionRef.get();
      
      subcollectionSnapshot.forEach((doc) => {
        batch.delete(doc.ref);
        console.log(`Marked ${subcollection} document for deletion: users/${uid}/${subcollection}/${doc.id}`);
      });
    }

    // 4. Remove user references from any shared collections
    // For example, remove user from class rosters, rankings, etc.
    await cleanupSharedCollections(uid, batch);

    // Execute the batch operation
    await batch.commit();
    
    console.log(`Successfully cleaned up all data for user: ${uid}`);
    
    // Log the cleanup for audit purposes
    await db.collection('admin_logs').add({
      action: 'user_data_cleanup',
      userId: uid,
      userEmail: user.email || 'unknown',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      status: 'success'
    });

  } catch (error) {
    console.error(`Error cleaning up data for user ${uid}:`, error);
    
    // Log the error for debugging
    await db.collection('admin_logs').add({
      action: 'user_data_cleanup',
      userId: uid,
      userEmail: user.email || 'unknown',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      status: 'error',
      error: error instanceof Error ? error.message : String(error)
    });
    
    throw error;
  }
});

/**
 * Helper function to clean up user references in shared collections
 */
async function cleanupSharedCollections(uid: string, batch: FirebaseFirestore.WriteBatch) {
  try {
    // Example: Remove user from class rosters
    const classesSnapshot = await db.collection('classes').get();
    
    classesSnapshot.forEach((classDoc) => {
      const classData = classDoc.data();
      if (classData.students && classData.students.includes(uid)) {
        const updatedStudents = classData.students.filter((studentId: string) => studentId !== uid);
        batch.update(classDoc.ref, { students: updatedStudents });
        console.log(`Removed user ${uid} from class ${classDoc.id}`);
      }
    });

    // Example: Remove user from rankings
    const rankingsSnapshot = await db.collection('rankings').where('userId', '==', uid).get();
    
    rankingsSnapshot.forEach((rankingDoc) => {
      batch.delete(rankingDoc.ref);
      console.log(`Removed ranking entry for user ${uid}`);
    });

    // Add more shared collection cleanup as needed
    
  } catch (error) {
    console.error('Error cleaning up shared collections:', error);
    throw error;
  }
}

/**
 * Manual cleanup function for existing orphaned data
 * Can be called via HTTP trigger for admin use
 */
export const manualCleanupOrphanedData = functions.https.onCall(async (data, context) => {
  // Verify admin authentication
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can perform manual cleanup');
  }

  const { userIds } = data;
  
  if (!userIds || !Array.isArray(userIds)) {
    throw new functions.https.HttpsError('invalid-argument', 'userIds must be an array');
  }

  console.log(`Manual cleanup requested for ${userIds.length} users`);
  
  const results = [];
  
  for (const uid of userIds) {
    try {
      // Check if user still exists in Auth
      try {
        await admin.auth().getUser(uid);
        console.log(`User ${uid} still exists in Auth, skipping cleanup`);
        results.push({ uid, status: 'skipped', reason: 'user_exists_in_auth' });
        continue;
      } catch (authError) {
        // User doesn't exist in Auth, proceed with cleanup
        console.log(`User ${uid} not found in Auth, proceeding with cleanup`);
      }

      // Perform the same cleanup as the automatic function
      const batch = db.batch();

      // Delete user document
      const userDocRef = db.collection('users').doc(uid);
      const userDoc = await userDocRef.get();
      
      if (userDoc.exists) {
        batch.delete(userDocRef);
        
        // Delete subcollections
        const assignmentsRef = db.collection('users').doc(uid).collection('assignments');
        const assignmentsSnapshot = await assignmentsRef.get();
        
        assignmentsSnapshot.forEach((doc) => {
          batch.delete(doc.ref);
        });

        // Clean up shared collections
        await cleanupSharedCollections(uid, batch);
        
        await batch.commit();
        
        results.push({ uid, status: 'cleaned', documentsDeleted: assignmentsSnapshot.size + 1 });
        console.log(`Successfully cleaned up orphaned data for user: ${uid}`);
      } else {
        results.push({ uid, status: 'not_found', reason: 'no_user_document' });
      }
      
    } catch (error) {
      console.error(`Error cleaning up user ${uid}:`, error);
      results.push({ uid, status: 'error', error: error instanceof Error ? error.message : String(error) });
    }
  }

  // Log the manual cleanup operation
  await db.collection('admin_logs').add({
    action: 'manual_cleanup_orphaned_data',
    adminUid: context.auth.uid,
    userIds: userIds,
    results: results,
    timestamp: admin.firestore.FieldValue.serverTimestamp()
  });

  return { success: true, results };
});

/**
 * Function to get list of orphaned user data
 * Returns users that exist in Firestore but not in Auth
 */
export const getOrphanedUserData = functions.https.onCall(async (data, context) => {
  // Verify admin authentication
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can view orphaned data');
  }

  console.log('Getting list of orphaned user data...');
  
  try {
    // Get all user documents from Firestore
    const usersSnapshot = await db.collection('users').get();
    const firestoreUsers = usersSnapshot.docs.map(doc => ({
      uid: doc.id,
      data: doc.data()
    }));

    console.log(`Found ${firestoreUsers.length} users in Firestore`);

    // Check which users don't exist in Auth
    const orphanedUsers = [];
    
    for (const user of firestoreUsers) {
      try {
        await admin.auth().getUser(user.uid);
        // User exists in Auth, not orphaned
      } catch (authError) {
        // User doesn't exist in Auth, it's orphaned
        orphanedUsers.push({
          uid: user.uid,
          email: user.data.email,
          fullName: user.data.fullName,
          createdAt: user.data.createdAt,
          lastUpdated: user.data.lastUpdated
        });
      }
    }

    console.log(`Found ${orphanedUsers.length} orphaned users`);

    return { 
      success: true, 
      orphanedUsers,
      totalFirestoreUsers: firestoreUsers.length,
      orphanedCount: orphanedUsers.length
    };
    
  } catch (error) {
    console.error('Error getting orphaned user data:', error);
    throw new functions.https.HttpsError('internal', 'Error retrieving orphaned data');
  }
});
