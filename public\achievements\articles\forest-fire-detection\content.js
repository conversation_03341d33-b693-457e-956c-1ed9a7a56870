// Forest Fire Detection Achievement Content
export const forestFireDetectionContent = {
    id: "forest-fire-detection",
    title: "🏅 Đạt giải Ba cuộc thi <PERSON><PERSON> học <PERSON> thuật cấp Tỉnh – Năm học 2024–2025",
    date: "20/05/2025",
    excerpt: "Hệ thống phát hiện sớm cháy rừng giúp ngăn chặn thiệt hại lớn cho môi trường đạt giải Ba cấp tỉnh Kon Tum.",
    images: [
        "../assets/images/achievements/forest-fire-detection-1.jpg",
        "../assets/images/achievements/forest-fire-detection-2.jpg",
        "../assets/images/achievements/forest-fire-detection-3.jpg"
    ],
    content: `
        <div class="project-info-section">
            <h2>📋 Thông tin dự án</h2>
            <div class="info-grid">
                <div class="info-item"><strong>👤 Họ và tên:</strong> <PERSON><PERSON></div>
                <div class="info-item"><strong>🏫 Trường:</strong> THCS và THPT Liên Việt Kon Tum</div>
                <div class="info-item"><strong>📍 Tỉnh/Thành phố:</strong> Kon Tum</div>
                <div class="info-item"><strong>📅 Thời gian:</strong> Năm học 2024–2025</div>
                <div class="info-item"><strong>📘 Lĩnh vực:</strong> Hệ thống nhúng</div>
                <div class="info-item"><strong>🔥 Tên dự án:</strong> Hệ thống phát hiện sớm cháy rừng giúp ngăn chặn thiệt hại lớn cho môi trường</div>
                <div class="info-item"><strong>🥉 Giải thưởng:</strong> Giải Ba – Cấp Tỉnh</div>
            </div>
        </div>

        <h2>🏆 Mô tả thành tích</h2>
        <p>Thành tích đạt được tại Cuộc thi Khoa học Kỹ thuật cấp Tỉnh gắn với các hoạt động STEM dành cho học sinh trung học năm học 2024–2025, do Sở Giáo dục và Đào tạo tỉnh Kon Tum tổ chức. Dự án được đánh giá cao nhờ khả năng ứng dụng thực tiễn trong công tác phòng chống cháy rừng, kết hợp giữa công nghệ xử lý hình ảnh và phân tích dữ liệu.</p>

        <h2>💡 Giới thiệu dự án</h2>
        <p>Dự án sử dụng công nghệ Thị giác máy tính (Computer Vision) để phát hiện dấu hiệu cháy rừng thông qua nhận diện các màu sắc đặc trưng như khói và lửa trên hình ảnh rừng.</p>

        <div class="feature-list">
            <div class="feature-item">
                <strong>📊 Phân tích khung hình theo thời gian thực:</strong> đánh giá mức độ nguy cơ cháy và đưa ra cảnh báo sớm.
            </div>
            <div class="feature-item">
                <strong>🎨 Ứng dụng xử lý ảnh:</strong> kết hợp ngưỡng màu sắc giúp nhận biết tình huống "Cháy" hoặc "Không cháy" với độ chính xác cao.
            </div>
            <div class="feature-item">
                <strong>🔍 Nhận diện màu sắc đặc trưng:</strong> phát hiện khói và lửa thông qua phân tích màu sắc trên hình ảnh rừng.
            </div>
        </div>

        <h2>🎯 Mục tiêu & Ý nghĩa</h2>
        <ul>
            <li>Góp phần nâng cao hiệu quả công tác phòng cháy chữa cháy rừng tại địa phương, giảm thiểu thiệt hại về người và tài nguyên thiên nhiên.</li>
            <li>Thúc đẩy việc tích hợp công nghệ thông minh vào bảo vệ môi trường.</li>
            <li>Khơi gợi ý thức bảo vệ rừng và nâng cao tinh thần sáng tạo trong học sinh trung học thông qua các hoạt động STEM.</li>
        </ul>

        <div class="certificate-info">
            <h3>📅 Ngày cấp giấy chứng nhận: 20/05/2025</h3>
        </div>
    `
};
