// Firebase configuration
import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
import { getFirestore, collection, addDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

const firebaseConfig = {
  apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
  authDomain: "classroom-web-48bc2.firebaseapp.com",
  projectId: "classroom-web-48bc2",
  storageBucket: "classroom-web-48bc2.firebasestorage.app",
  messagingSenderId: "446746787502",
  appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
  measurementId: "G-742XRP9E96"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
const db = getFirestore(app);

// Function to submit registration data to Firebase
async function submitRegistration(formData) {
  try {
    // Add a new document with a generated ID
    const docRef = await addDoc(collection(db, "registrations"), formData);
    console.log("Registration successful with ID: ", docRef.id);
    return {
      success: true,
      id: docRef.id
    };
  } catch (error) {
    console.error("Error adding registration: ", error);
    return {
      success: false,
      error: error.message
    };
  }
}

export { app, db, submitRegistration }; 