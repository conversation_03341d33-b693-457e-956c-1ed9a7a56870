// UI components for achievements

// Create achievement card element
export function createAchievementCard(achievement, stats, currentUser) {
    const card = document.createElement('div');
    card.className = 'achievement-card';
    card.dataset.id = achievement.id;

    const isLiked = currentUser && stats.likedBy && stats.likedBy.includes(currentUser.uid);

    card.innerHTML = `
        <div class="achievement-image">
            <img src="${achievement.images[0]}" alt="${achievement.title}">
        </div>
        <div class="achievement-details">
            <div class="achievement-date">${achievement.date}</div>
            <h3 class="achievement-title">${achievement.title}</h3>
            <p class="achievement-excerpt">${achievement.excerpt}</p>
            <div class="achievement-stats">
                <div class="stat-item">
                    <i class="fas fa-eye"></i>
                    <span>${stats.views || 0}</span>
                </div>
                <button class="like-btn ${isLiked ? 'liked' : ''}" onclick="handleLikeClick('${achievement.id}', event)">
                    <i class="fas fa-heart"></i>
                    <span>${stats.likes || 0}</span>
                </button>
            </div>
            <a href="#" class="read-more" onclick="handleReadMore('${achievement.id}', event)">
                Xem thêm <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    `;

    return card;
}

// Update modal content
export function updateModalContent(achievement, stats, currentUser) {
    // Update modal content
    document.querySelector('.modal-title').textContent = achievement.title;
    document.querySelector('.modal-date').textContent = achievement.date;
    document.getElementById('modalViewCount').textContent = stats.views || 0;
    document.getElementById('modalLikeCount').textContent = stats.likes || 0;
    document.getElementById('modalBody').innerHTML = achievement.content;

    // Update images
    const modalImages = document.getElementById('modalImages');
    modalImages.innerHTML = '';
    achievement.images.forEach(imageSrc => {
        const img = document.createElement('img');
        img.src = imageSrc;
        img.alt = achievement.title;
        img.className = 'modal-image';
        img.onclick = () => openImageFullscreen(imageSrc);
        modalImages.appendChild(img);
    });

    // Update like button
    const modalLikeBtn = document.getElementById('modalLikeBtn');
    const isLiked = currentUser && stats.likedBy && stats.likedBy.includes(currentUser.uid);

    modalLikeBtn.className = `modal-like-btn ${isLiked ? 'liked' : ''}`;
    modalLikeBtn.innerHTML = `
        <i class="fas fa-heart"></i>
        <span>${isLiked ? 'Đã thích' : 'Thích bài viết'}</span>
    `;

    if (!currentUser) {
        modalLikeBtn.innerHTML = `
            <i class="fas fa-heart"></i>
            <span>Đăng nhập để thích</span>
        `;
    }
}

// Open image in fullscreen
export function openImageFullscreen(imageSrc) {
    const fullscreenDiv = document.createElement('div');
    fullscreenDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        z-index: 2000;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = imageSrc;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    `;

    fullscreenDiv.appendChild(img);
    fullscreenDiv.onclick = () => document.body.removeChild(fullscreenDiv);
    document.body.appendChild(fullscreenDiv);
}

// Show loading state
export function showLoading(container) {
    container.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner"></i>
            <p>Đang tải thành tích...</p>
        </div>
    `;
}

// Show error state
export function showError(container, message = 'Không thể tải thành tích. Vui lòng thử lại sau.') {
    container.innerHTML = `
        <div class="no-achievements">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Lỗi tải dữ liệu</h3>
            <p>${message}</p>
        </div>
    `;
}

// Show empty state
export function showEmpty(container) {
    container.innerHTML = `
        <div class="no-achievements">
            <i class="fas fa-trophy"></i>
            <h3>Chưa có thành tích nào</h3>
            <p>Thành tích sẽ được cập nhật sớm</p>
        </div>
    `;
}
