// Quiz data for Assignment 1: CNTT and Programming Basics
const quizQuestions = [
    {
        question: "Công nghệ Thông tin (CNTT) là ngành khoa học về việc gì?",
        options: [
            "Chế tạo máy móc cơ khí",
            "<PERSON><PERSON> thập, <PERSON><PERSON> lý, lưu trữ và truyền tải thông tin bằng máy tính",
            "<PERSON><PERSON>ên cứu các hành tinh trong vũ trụ",
            "Trồng trọt và chăn nuôi hiện đại"
        ],
        correct: 1,
        explanation: "Công nghệ Thông tin (CNTT) tập trung vào việc nghiên cứu và ứng dụng các hệ thống máy tính để thu thập, x<PERSON> lý, lưu trữ, và truyền tải thông tin một cách hiệu quả."
    },
    {
        question: "Công cụ nào sau đây được coi là \"ông tổ\" sơ khai của má<PERSON> t<PERSON>, gi<PERSON><PERSON> con người thực hiện các phép cộng trừ cơ bản?",
        options: [
            "Kính thiên văn",
            "Bàn tính Abacus",
            "La bàn",
            "Máy ảnh"
        ],
        correct: 1,
        explanation: "Bàn tính Abacus là một trong những công cụ tính toán lâu đời và sơ khai nhất, được sử dụng rộng rãi để thực hiện các phép tính số học cơ bản trước khi có máy tính điện tử."
    },
    {
        question: "Yếu tố công nghệ nào sau đây là đặc trưng chính của thế hệ máy tính điện tử đầu tiên (ví dụ như ENIAC)?",
        options: [
            "Vi mạch (Microchip)",
            "Bóng bán dẫn (Transistor)",
            "Bóng đèn chân không (Vacuum Tube)",
            "Đĩa từ (Magnetic Disk)"
        ],
        correct: 2,
        explanation: "Thế hệ máy tính điện tử đầu tiên (khoảng thập niên 1940-1950) như ENIAC và UNIVAC I chủ yếu sử dụng bóng đèn chân không làm linh kiện chính, khiến chúng có kích thước rất lớn, tiêu thụ nhiều năng lượng và tỏa nhiệt cao."
    },
    {
        question: "Phát minh nào sau đây đã tạo ra cuộc cách mạng, giúp thu nhỏ kích thước máy tính và tăng tốc độ xử lý lên rất nhiều, mở đường cho máy tính cá nhân?",
        options: [
            "Bóng đèn chân không",
            "Băng từ",
            "Vi mạch (Microchip/IC)",
            "Đĩa mềm"
        ],
        correct: 2,
        explanation: "Vi mạch tích hợp (Integrated Circuit - IC hay Microchip), được phát minh vào cuối thập niên 1950, đã cho phép tích hợp hàng ngàn đến hàng triệu linh kiện điện tử siêu nhỏ lên một con chip duy nhất, từ đó giảm đáng kể kích thước, chi phí và tăng hiệu năng của máy tính."
    },
    {
        question: "World Wide Web (WWW) đã mang lại lợi ích cốt lõi nào cho người dùng Internet?",
        options: [
            "Tăng tốc độ kết nối Internet lên gấp nhiều lần.",
            "Cung cấp một hệ thống dễ sử dụng để truy cập và chia sẻ thông tin thông qua các trang web và siêu liên kết.",
            "Mã hóa tất cả dữ liệu truyền đi trên Internet để tăng cường bảo mật.",
            "Cho phép các máy tính giao tiếp trực tiếp với nhau mà không cần máy chủ."
        ],
        correct: 1,
        explanation: "WWW là một hệ thống thông tin liên kết toàn cầu, cho phép người dùng truy cập các tài liệu (trang web) được liên kết với nhau qua các siêu liên kết, dễ dàng tìm kiếm và chia sẻ thông tin một cách trực quan thông qua trình duyệt web."
    },
    {
        question: "Cuộc cách mạng di động với sự ra đời của điện thoại thông minh (smartphone) đã mang lại thay đổi lớn nào trong cách con người sử dụng công nghệ?",
        options: [
            "Chỉ làm tăng số lượng cuộc gọi điện thoại.",
            "Giúp mọi người có thể truy cập Internet, ứng dụng và thông tin mọi lúc, mọi nơi.",
            "Khiến máy tính để bàn trở nên lỗi thời hoàn toàn.",
            "Chủ yếu phục vụ cho việc chơi game."
        ],
        correct: 1,
        explanation: "Smartphone đã biến thiết bị di động thành một trung tâm thông tin và giải trí đa năng, cho phép người dùng luôn kết nối với Internet, sử dụng hàng ngàn ứng dụng và truy cập thông tin mọi lúc, mọi nơi, thay đổi sâu sắc cách chúng ta làm việc, học tập và giải trí."
    },
    {
        question: "Công nghệ nào cho phép các thiết bị vật lý (đèn, tủ lạnh, đồng hồ) kết nối Internet và trao đổi dữ liệu với nhau?",
        options: [
            "Trí tuệ Nhân tạo (AI)",
            "Vạn vật Kết nối (IoT)",
            "Thực tế Ảo (VR)",
            "Blockchain"
        ],
        correct: 1,
        explanation: "Vạn vật Kết nối (IoT - Internet of Things) là mạng lưới các thiết bị vật lý, xe cộ, đồ gia dụng và các vật thể khác được nhúng cảm biến, phần mềm và các công nghệ khác cho phép chúng kết nối và trao đổi dữ liệu qua Internet."
    },
    {
        question: "Giai đoạn nào trong lịch sử phát triển CNTT được đánh dấu bằng sự ra đời của những chiếc máy tính sử dụng chủ yếu bóng đèn chân không?",
        options: [
            "Thời kỳ sơ khai (trước thế kỷ 20)",
            "Kỷ nguyên máy tính lớn (Giữa thế kỷ 20)",
            "Kỷ nguyên máy tính cá nhân (Cuối thế kỷ 20)",
            "Kỷ nguyên Internet và Di động (Cuối TK 20 - Nay)"
        ],
        correct: 1,
        explanation: "Kỷ nguyên máy tính lớn (Mainframe Era), đặc biệt là giai đoạn từ những năm 1940 đến giữa những năm 1950, chứng kiến sự xuất hiện của các máy tính điện tử đầu tiên sử dụng bóng đèn chân không làm linh kiện chính."
    },
    {
        question: "Máy tính Pascaline của Blaise Pascal có khả năng thực hiện chủ yếu những phép tính nào?",
        options: [
            "Cộng và Trừ",
            "Nhân và Chia",
            "Khai căn và Lũy thừa",
            "Tất cả các phép tính trên"
        ],
        correct: 0,
        explanation: "Pascaline là một trong những máy tính cơ học đầu tiên có khả năng thực hiện phép cộng và trừ một cách tự động. Các phép tính nhân và chia phức tạp hơn được thực hiện bằng cách lặp lại phép cộng và trừ."
    },
    {
        question: "\"Máy Turing\" do Alan Turing đề xuất là gì?",
        options: [
            "Một cỗ máy giải mã mật mã Enigma",
            "Một mô hình lý thuyết về máy tính đa năng, đặt nền móng cho khoa học máy tính",
            "Máy tính cá nhân đầu tiên trên thế giới",
            "Một trình duyệt web"
        ],
        correct: 1,
        explanation: "Máy Turing là một mô hình trừu tượng được Alan Turing đưa ra vào năm 1936 để định nghĩa khái niệm thuật toán và tính toán. Nó là nền tảng lý thuyết cho sự phát triển của máy tính hiện đại và khoa học máy tính."
    },
    {
        question: "Sự phát triển của Giao diện Đồ họa Người dùng (GUI) có ý nghĩa quan trọng như thế nào đối với việc phổ biến máy tính?",
        options: [
            "Làm cho máy tính chạy nhanh hơn.",
            "Giúp việc lập trình máy tính trở nên đơn giản hơn cho các chuyên gia.",
            "Giảm chi phí sản xuất máy tính.",
            "Làm cho máy tính trở nên dễ sử dụng và trực quan hơn đối với người dùng phổ thông, không cần phải nhớ các lệnh phức tạp."
        ],
        correct: 3,
        explanation: "GUI (Graphical User Interface) đã thay thế giao diện dòng lệnh bằng các biểu tượng, cửa sổ, menu và con trỏ, giúp người dùng tương tác với máy tính một cách trực quan và dễ dàng hơn rất nhiều, làm cho máy tính tiếp cận được với đông đảo công chúng."
    },
    {
        question: "Sự ra đời của máy tính cá nhân (PC) vào cuối thế kỷ 20 có ý nghĩa cốt lõi gì?",
        options: [
            "Làm cho các máy tính lớn (mainframes) trở nên vô dụng.",
            "Đưa sức mạnh tính toán đến gần hơn với người dùng cá nhân và doanh nghiệp nhỏ, thay vì chỉ tập trung ở các tổ chức lớn.",
            "Chỉ phục vụ mục đích giải trí và chơi game.",
            "Khiến việc lập trình trở nên khó khăn hơn do sự đa dạng của phần cứng."
        ],
        correct: 1,
        explanation: "Máy tính cá nhân đã dân chủ hóa công nghệ, đưa khả năng xử lý thông tin mạnh mẽ từ các trung tâm máy tính lớn đến bàn làm việc của từng cá nhân, doanh nghiệp nhỏ và hộ gia đình, thúc đẩy sự phát triển của nhiều ứng dụng và phần mềm."
    },
    {
        question: "Công nghệ nào sau đây cho phép người dùng đắm chìm vào một môi trường hoàn toàn nhân tạo, mô phỏng bằng máy tính?",
        options: [
            "Thực tế Tăng cường (AR)",
            "Internet of Things (IoT)",
            "Thực tế Ảo (VR)",
            "Điện toán Đám mây (Cloud Computing)"
        ],
        correct: 2,
        explanation: "Thực tế Ảo (VR - Virtual Reality) là công nghệ tạo ra một môi trường giả lập hoàn toàn bằng máy tính, đưa người dùng vào một thế giới ảo thông qua các thiết bị chuyên dụng như kính VR."
    },
    {
        question: "Khái niệm \"máy tính lưu trữ chương trình\" (stored-program computer) có ý nghĩa quan trọng như thế nào đối với sự phát triển của máy tính?",
        options: [
            "Cho phép máy tính chạy nhanh hơn gấp nhiều lần.",
            "Làm cho máy tính trở nên nhỏ gọn hơn.",
            "Cho phép chương trình (tập hợp các lệnh) được lưu trữ trong bộ nhớ của máy tính và dễ dàng thay đổi, tăng tính linh hoạt và đa năng của máy.",
            "Giúp máy tính kết nối được với Internet."
        ],
        correct: 2,
        explanation: "Kiến trúc máy tính lưu trữ chương trình (kiến trúc Von Neumann) là một bước đột phá, cho phép máy tính lưu trữ cả dữ liệu và chương trình trong cùng một bộ nhớ, giúp máy tính trở nên linh hoạt và có thể thực hiện nhiều tác vụ khác nhau chỉ bằng cách thay đổi chương trình."
    },
    {
        question: "Sự khác biệt cơ bản nhất giữa Internet và World Wide Web là gì?",
        options: [
            "Internet là phần mềm, World Wide Web là phần cứng.",
            "Internet là mạng lưới kết nối vật lý và giao thức, World Wide Web là hệ thống thông tin chạy trên Internet.",
            "World Wide Web ra đời trước Internet.",
            "Internet chỉ dùng cho quân sự, World Wide Web dùng cho dân sự."
        ],
        correct: 1,
        explanation: "Internet là một cơ sở hạ tầng mạng toàn cầu, bao gồm các thiết bị phần cứng (máy chủ, router, cáp quang) và giao thức truyền thông (TCP/IP). World Wide Web là một trong những ứng dụng chạy trên Internet, là một hệ thống thông tin liên kết các tài liệu và tài nguyên khác bằng siêu liên kết, truy cập qua trình duyệt web."
    },
    {
        question: "Máy tính TRADIC (1954) có ý nghĩa lịch sử gì quan trọng?",
        options: [
            "Là máy tính đầu tiên sử dụng kiến trúc Von Neumann.",
            "Là máy tính thương mại đầu tiên được bán hàng loạt.",
            "Là máy tính đầu tiên hoàn toàn sử dụng bóng bán dẫn (transistor), đánh dấu sự chuyển giao công nghệ quan trọng.",
            "Là máy tính đầu tiên có khả năng hiển thị đồ họa màu."
        ],
        correct: 2,
        explanation: "TRADIC (Transistor Digital Computer) được Bell Labs hoàn thành năm 1954, là máy tính đầu tiên hoàn toàn sử dụng transistor thay vì bóng đèn chân không. Điều này mở ra kỷ nguyên thứ hai của máy tính, với ưu điểm về kích thước nhỏ hơn, tiêu thụ ít năng lượng hơn và đáng tin cậy hơn."
    },
    {
        question: "Yếu tố nào KHÔNG phải là lý do chính cho sự trỗi dậy mạnh mẽ của Trí tuệ Nhân tạo (AI) trong những năm gần đây?",
        options: [
            "Sự gia tăng của Dữ liệu lớn (Big Data).",
            "Sự phát triển của các thuật toán Học Sâu (Deep Learning).",
            "Sự phổ biến của các máy tính cơ học.",
            "Năng lực tính toán mạnh mẽ của GPU và chip AI chuyên dụng."
        ],
        correct: 2,
        explanation: "Sự trỗi dậy của AI hiện đại được thúc đẩy bởi sự sẵn có của lượng lớn dữ liệu (Big Data), các thuật toán tiên tiến như Học Sâu, và khả năng xử lý mạnh mẽ từ các bộ xử lý đồ họa (GPU) và chip AI chuyên dụng. Máy tính cơ học là công nghệ lỗi thời và không đóng góp vào sự phát triển AI hiện nay."
    },
    {
        question: "Mục đích chính của việc sử dụng các công cụ tính toán cơ học sơ khai như Pascaline hay Máy Sai Phân của Babbage là gì?",
        options: [
            "Để chơi các trò chơi điện tử phức tạp.",
            "Để tự động hóa các phép tính toán học, giảm thiểu sai sót do con người và tăng tốc độ tính toán cho các bảng số liệu.",
            "Để thiết kế các giao diện đồ họa cho máy tính.",
            "Để kết nối các máy tính lại với nhau thành một mạng lưới."
        ],
        correct: 1,
        explanation: "Các công cụ này được thiết kế để thực hiện các phép tính số học một cách tự động và chính xác, nhằm giảm gánh nặng tính toán thủ công và loại bỏ lỗi trong việc lập bảng số liệu (ví dụ: bảng hàng hải, bảng logarit)."
    },
    {
        question: "Việc giải mã thành công mật mã Enigma của Đức Quốc xã trong Thế chiến II, với đóng góp quan trọng của Alan Turing, đã:",
        options: [
            "Dẫn đến sự phát minh ra Internet.",
            "Cho thấy sức mạnh của tính toán và giải thuật trong việc giải quyết các vấn đề phức tạp, có tác động lớn đến cục diện lịch sử.",
            "Trực tiếp tạo ra máy tính cá nhân đầu tiên.",
            "Không có tác động đáng kể đến cục diện chiến tranh."
        ],
        correct: 1,
        explanation: "Dự án giải mã Enigma tại Bletchley Park, với sự tham gia của Alan Turing và các máy tính điện cơ như Bombe, đã chứng minh tầm quan trọng của việc tự động hóa tính toán và áp dụng các giải thuật phức tạp để phá vỡ mật mã, rút ngắn chiến tranh và đặt nền móng cho ngành khoa học máy tính."
    },
    {
        question: "Tại sao việc xem xét các khía cạnh đạo đức (ví dụ: thiên kiến, quyền riêng tư) lại ngày càng trở nên quan trọng trong quá trình phát triển và ứng dụng Trí tuệ Nhân tạo (AI)?",
        options: [
            "Vì AI tiêu thụ quá nhiều năng lượng, ảnh hưởng đến môi trường.",
            "Vì các thuật toán AI có thể phức tạp đến mức con người không thể hiểu được cách chúng hoạt động.",
            "Vì AI có khả năng đưa ra các quyết định ảnh hưởng lớn đến đời sống con người và xã hội, và nếu không được thiết kế cẩn thận, chúng có thể gây ra những hậu quả tiêu cực hoặc bất công.",
            "Vì việc phát triển AI quá tốn kém và không mang lại lợi ích kinh tế rõ ràng."
        ],
        correct: 2,
        explanation: "AI ngày càng được tích hợp vào các lĩnh vực quan trọng như y tế, pháp luật, tài chính, và tuyển dụng. Nếu AI được xây dựng dựa trên dữ liệu có thiên kiến hoặc không xem xét quyền riêng tư, nó có thể dẫn đến các quyết định phân biệt đối xử, vi phạm quyền riêng tư và gây ra những tác động tiêu cực nghiêm trọng đến cá nhân và xã hội."
    },
    // Phần 2: Kiến thức cơ bản về Lập trình
    {
        question: "Ngôn ngữ lập trình là gì?",
        options: [
            "Ngôn ngữ để máy tính nói chuyện với nhau",
            "Ngôn ngữ giao tiếp giữa các lập trình viên",
            "Ngôn ngữ chung giữa người và máy tính",
            "Một phần mềm soạn thảo văn bản"
        ],
        correct: 2,
        explanation: "Ngôn ngữ lập trình là một tập hợp các quy tắc cú pháp và ngữ nghĩa được sử dụng để tạo ra các chương trình máy tính. Nó đóng vai trò là cầu nối, cho phép con người giao tiếp và ra lệnh cho máy tính thực hiện các tác vụ cụ thể."
    },
    {
        question: "Sản phẩm nào KHÔNG được tạo ra từ lập trình?",
        options: [
            "Trò chơi điện tử",
            "Ứng dụng điện thoại",
            "Website",
            "Bàn ghế gỗ"
        ],
        correct: 3,
        explanation: "Bàn ghế gỗ là sản phẩm thủ công, không được tạo ra từ lập trình. Các sản phẩm công nghệ như trò chơi điện tử, ứng dụng điện thoại và website đều được xây dựng từ mã lệnh của các ngôn ngữ lập trình."
    },
    {
        question: "Điều nào sau đây KHÔNG phải là một ngôn ngữ lập trình?",
        options: [
            "Python",
            "Java",
            "JavaScript",
            "Microsoft Word"
        ],
        correct: 3,
        explanation: "Microsoft Word là một phần mềm xử lý văn bản, không phải ngôn ngữ lập trình. Python, Java và JavaScript đều là các ngôn ngữ lập trình phổ biến được sử dụng để phát triển phần mềm, ứng dụng và website."
    },
    {
        question: "Lập trình là gì?",
        options: [
            "Cách 'nói chuyện' với máy tính bằng các chỉ dẫn (mã lệnh)",
            "Hoạt động sửa chữa máy tính bị hỏng",
            "Việc vẽ tranh bằng máy tính",
            "Quản lý các file trên máy tính"
        ],
        correct: 0,
        explanation: "Lập trình là quá trình viết các chỉ dẫn (còn gọi là mã lệnh hay code) mà máy tính có thể hiểu và thực hiện để hoàn thành một nhiệm vụ hoặc tạo ra một ứng dụng cụ thể."
    },
    {
        question: "Lập trình giúp bạn làm gì với ý tưởng của mình?",
        options: [
            "Biến ý tưởng thành hiện thực (game, app, web...)",
            "Chỉ giúp bạn lưu trữ ý tưởng",
            "Giúp bạn quên đi ý tưởng",
            "Chỉ để nói về ý tưởng với người khác"
        ],
        correct: 0,
        explanation: "Lập trình là công cụ cho phép bạn hiện thực hóa các ý tưởng sáng tạo thành các sản phẩm công nghệ cụ thể như trò chơi, ứng dụng di động, trang web, hoặc các hệ thống tự động hóa."
    },
    {
        question: "Lập trình giúp phát triển kỹ năng nào?",
        options: [
            "Thể lực",
            "Tư duy logic",
            "Kỹ năng nấu ăn",
            "Khả năng bơi lội"
        ],
        correct: 1,
        explanation: "Lập trình đòi hỏi người học phải suy nghĩ một cách mạch lạc, phân tích vấn đề thành các bước nhỏ hơn và tìm ra giải pháp có trình tự, từ đó giúp rèn luyện và phát triển kỹ năng tư duy logic và giải quyết vấn đề."
    },
    {
        question: "Học lập trình giúp bạn hiểu gì về thế giới công nghệ?",
        options: [
            "Chỉ biết cách chơi game giỏi hơn",
            "Hiểu cách công nghệ hoạt động, không chỉ là người dùng",
            "Chỉ biết cách sử dụng Word và Excel",
            "Không liên quan đến việc hiểu công nghệ"
        ],
        correct: 1,
        explanation: "Học lập trình không chỉ giúp bạn sử dụng công nghệ mà còn giúp bạn hiểu sâu sắc cách các phần mềm và hệ thống hoạt động, từ đó bạn có thể trở thành người tạo ra công nghệ chứ không chỉ là người tiêu dùng."
    },
    {
        question: "Tại sao học lập trình giúp rèn luyện tư duy logic?",
        options: [
            "Vì không cần phải suy nghĩ nhiều khi lập trình",
            "Vì phải suy nghĩ mạch lạc, giải quyết vấn đề từng bước một",
            "Vì chỉ cần học thuộc lòng các câu lệnh",
            "Vì lập trình liên quan đến các con số ngẫu nhiên"
        ],
        correct: 1,
        explanation: "Lập trình là một quá trình đòi hỏi sự logic cao: bạn phải chia nhỏ vấn đề, xác định các bước giải quyết tuần tự và xây dựng một chuỗi lệnh rõ ràng để máy tính thực hiện. Quá trình này giúp rèn luyện khả năng tư duy logic và giải quyết vấn đề có hệ thống."
    },
    {
        question: "Lập trình có thể ứng dụng trong lĩnh vực nào?",
        options: [
            "Phát triển game",
            "Phát triển ứng dụng di động",
            "Xây dựng website",
            "Tất cả các lĩnh vực trên"
        ],
        correct: 3,
        explanation: "Lập trình là một kỹ năng đa năng và có thể được ứng dụng trong hầu hết mọi lĩnh vực hiện đại, từ y tế, tài chính, giáo dục, nghệ thuật, tự động hóa, đến phát triển game, ứng dụng di động, website và trí tuệ nhân tạo."
    },
    {
        question: "Python được coi là ngôn ngữ phù hợp cho người mới vì?",
        options: [
            "Rất khó học và cú pháp phức tạp",
            "Chỉ dùng để phát triển các ứng dụng rất lớn",
            "Dễ học, dễ đọc và có cú pháp đơn giản",
            "Không có tài liệu hướng dẫn nào"
        ],
        correct: 2,
        explanation: "Python nổi tiếng với cú pháp rõ ràng, gần gũi với ngôn ngữ tự nhiên và dễ đọc, giúp người mới bắt đầu lập trình dễ dàng nắm bắt các khái niệm cơ bản mà không bị choáng ngợp bởi sự phức tạp của cú pháp."
    }
];

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = quizQuestions;
}
