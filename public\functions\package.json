{"name": "classroom-web-functions", "description": "Cloud Functions for classroom web application", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"typescript": "^4.9.0", "@types/node": "^18.0.0"}, "private": true}