# Achievements Section - Hướng Dẫn Sử Dụng

## C<PERSON>u <PERSON>r<PERSON>c <PERSON>

```
achievements/
├── index.html                 # Trang chính hiển thị achievements
├── data/
│   └── achievements-data.js   # File cấu hình chính import tất cả achievements
├── js/
│   ├── firebase-utils.js      # Utilities cho Firebase operations
│   └── ui-components.js       # Components UI tái sử dụng
├── articles/                  # Thư mục chứa nội dung từng achievement
│   ├── covid-app/
│   │   └── content.js         # Nội dung achievement COVID app
│   └── heart-disease-prediction/
│       └── content.js         # Nội dung achievement dự đoán bệnh tim
└── README.md                  # File hướng dẫn này
```

## Cách Thêm Achievement Mới

### Bước 1: Tạo thư mục cho achievement mới
```
achievements/articles/ten-achievement-moi/
└── content.js
```

### Bước 2: Tạo file content.js
```javascript
// File: achievements/articles/ten-achievement-moi/content.js
export const tenAchievementMoiContent = {
    id: "ten-achievement-moi",
    title: "Tiêu đề achievement",
    date: "Năm XXXX",
    excerpt: "Mô tả ngắn gọn về achievement...",
    images: [
        "../assets/images/achievements/hinh-anh-1.jpg",
        "../assets/images/achievements/hinh-anh-2.jpg"
    ],
    content: `
        <p>Nội dung chi tiết về achievement...</p>
        <p><strong>Các điểm nổi bật:</strong></p>
        <ul>
            <li>Điểm 1</li>
            <li>Điểm 2</li>
        </ul>
    `
};
```

### Bước 3: Cập nhật file achievements-data.js
```javascript
// File: achievements/data/achievements-data.js
import { covidAppContent } from '../articles/covid-app/content.js';
import { heartDiseasePredictionContent } from '../articles/heart-disease-prediction/content.js';
import { tenAchievementMoiContent } from '../articles/ten-achievement-moi/content.js'; // Thêm dòng này

export const achievementsData = {
    "covid-app": covidAppContent,
    "heart-disease-prediction": heartDiseasePredictionContent,
    "ten-achievement-moi": tenAchievementMoiContent // Thêm dòng này
};
```

### Bước 4: Thêm hình ảnh
- Đặt hình ảnh vào thư mục `assets/images/achievements/`
- Đảm bảo đường dẫn trong file content.js chính xác

## Tính Năng

### Firebase Integration
- **View tracking**: Tự động đếm lượt xem khi người dùng mở modal
- **Like system**: Người dùng có thể thích/bỏ thích achievements (cần đăng nhập)
- **Statistics**: Hiển thị số lượt xem và lượt thích

### UI Components
- **Responsive design**: Tự động điều chỉnh theo kích thước màn hình
- **Modal view**: Xem chi tiết achievement trong popup
- **Image gallery**: Hiển thị nhiều hình ảnh, có thể xem fullscreen
- **Loading states**: Hiển thị trạng thái loading và error

### Error Handling
- Xử lý lỗi Firebase permissions
- Fallback cho trường hợp không có dữ liệu
- Graceful degradation khi không đăng nhập

## Lưu Ý Kỹ Thuật

### Firebase Permissions
- Collection "achievements" cần được cấu hình permissions phù hợp
- Guest users có thể xem nhưng không thể like
- Authenticated users có thể like/unlike

### Performance
- Lazy loading cho hình ảnh
- Efficient Firebase queries
- Minimal DOM manipulation

### Maintenance
- Mỗi achievement có file riêng để dễ quản lý
- Centralized configuration trong achievements-data.js
- Reusable components trong js/ui-components.js

## Troubleshooting

### Lỗi Firebase Permissions
- Kiểm tra Firestore rules
- Đảm bảo collection "achievements" tồn tại
- Verify Firebase config

### Lỗi Import Modules
- Kiểm tra đường dẫn import
- Đảm bảo file được export đúng cách
- Verify browser support cho ES6 modules

### Lỗi Hiển Thị Hình Ảnh
- Kiểm tra đường dẫn hình ảnh
- Đảm bảo file tồn tại trong assets/images/achievements/
- Verify permissions truy cập file
