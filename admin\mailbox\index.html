<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> <PERSON>ư - Admin Panel</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .mailbox-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .page-header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            background: #e2e8f0;
            color: #4a5568;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .back-btn:hover {
            background: #cbd5e0;
            color: #2d3748;
            transform: translateY(-2px);
        }



        .mailbox-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-section {
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        select.form-control {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
            appearance: none;
        }

        select.form-control:focus {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        }

        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .recipient-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .recipient-option {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .recipient-option:hover {
            border-color: #667eea;
            background: #ebf8ff;
        }

        .recipient-option.selected {
            border-color: #667eea;
            background: #ebf8ff;
            color: #667eea;
        }

        .recipient-option i {
            font-size: 1.5rem;
            margin-bottom: 8px;
            display: block;
        }

        .send-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .preview-section {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }

        .preview-message {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .preview-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .preview-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .preview-info h4 {
            margin: 0;
            color: #2d3748;
        }

        .preview-info p {
            margin: 5px 0 0 0;
            color: #718096;
            font-size: 0.9rem;
        }



        .loading {
            display: none;
        }

        .loading.show {
            display: inline-block;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../classes/">Lớp Học</a></li>
                    <li><a href="../../achievements/">Thành Tích</a></li>
                    <li><a href="../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../auth/">Tài Khoản</a></li>
                    <li><a href="../" class="active">Quản Trị</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="mailbox-container">
                <div class="page-header">
                    <h1><i class="fas fa-inbox"></i> Quản Lý Hộp Thư</h1>
                    <a href="../" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>

            <div class="mailbox-form">
                <form id="messageForm">
                    <div class="form-section">
                        <h3><i class="fas fa-user-edit"></i> Thông Tin Người Gửi</h3>
                        <div class="form-group">
                            <label for="senderName">Tên người gửi:</label>
                            <input type="text" id="senderName" class="form-control" value="Admin Vthon Academy" required>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-users"></i> Người Nhận</h3>
                        <div class="recipient-options">
                            <div class="recipient-option selected" data-type="all">
                                <i class="fas fa-users"></i>
                                <div>Tất Cả Học Viên</div>
                            </div>
                            <div class="recipient-option" data-type="class">
                                <i class="fas fa-school"></i>
                                <div>Theo Lớp Học</div>
                            </div>
                            <div class="recipient-option" data-type="individual">
                                <i class="fas fa-user"></i>
                                <div>Cá Nhân</div>
                            </div>
                        </div>
                        <div id="classSelector" class="form-group" style="display: none; margin-top: 15px;">
                            <label for="targetClass">Chọn lớp:</label>
                            <select id="targetClass" class="form-control">
                                <option value="">-- Chọn lớp học --</option>
                                <option value="python-a">Python A</option>
                                <option value="python-b">Python B</option>
                                <option value="python-c">Python C</option>
                            </select>
                        </div>
                        <div id="emailSelector" class="form-group" style="display: none; margin-top: 15px;">
                            <label for="targetEmail">Email học viên:</label>
                            <input type="email" id="targetEmail" class="form-control" placeholder="Nhập email học viên">
                        </div>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-envelope"></i> Nội Dung Tin Nhắn</h3>

                        <!-- Mail Templates -->
                        <div class="form-group">
                            <label for="mailTemplate">Chọn mẫu tin nhắn:</label>
                            <select id="mailTemplate" class="form-control">
                                <option value="">-- Chọn mẫu tin nhắn --</option>
                                <option value="fee_reminder">Thông báo nộp học phí</option>
                                <option value="new_lesson">Thông báo có bài học mới</option>
                                <option value="new_badge">Thông báo có huy hiệu mới</option>
                                <option value="new_event">Thông báo sự kiện mới</option>
                                <option value="assignment_reminder">Nhắc nhở làm bài tập</option>
                                <option value="ranking_update">Cập nhật bảng xếp hạng</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="messageTitle">Tiêu đề:</label>
                            <input type="text" id="messageTitle" class="form-control" placeholder="Nhập tiêu đề tin nhắn" required>
                        </div>
                        <div class="form-group">
                            <label for="messageContent">Nội dung:</label>
                            <textarea id="messageContent" class="form-control" placeholder="Nhập nội dung tin nhắn..." required></textarea>
                        </div>
                    </div>

                    <button type="submit" class="send-button">
                        <i class="fas fa-paper-plane"></i>
                        <span class="loading"><i class="fas fa-spinner"></i></span>
                        <span class="button-text">Gửi Thông Báo</span>
                    </button>
                </form>
            </div>

            <div class="preview-section">
                <h3><i class="fas fa-eye"></i> Xem Trước Tin Nhắn</h3>
                <div class="preview-message">
                    <div class="preview-header">
                        <div class="preview-avatar" id="previewAvatar">A</div>
                        <div class="preview-info">
                            <h4 id="previewSender">Admin Vthon Academy</h4>
                            <p id="previewTime">Vừa xong</p>
                        </div>
                    </div>
                    <div class="preview-content">
                        <h4 id="previewTitle">Tiêu đề tin nhắn sẽ hiển thị ở đây</h4>
                        <p id="previewContent">Nội dung tin nhắn sẽ hiển thị ở đây...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module" src="js/mailbox.js"></script>
</body>
</html>
