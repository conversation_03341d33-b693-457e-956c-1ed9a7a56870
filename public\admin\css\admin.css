/* Admin Styles */
.admin-main {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 6rem 0 2rem 0; /* Top padding để tránh bị header che */
}

.admin-header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-header h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    white-space: nowrap;
    overflow: visible;
    flex-shrink: 0;
}

.admin-header h1 i {
    color: #e74c3c;
    margin-right: 0.5rem;
}

.admin-info {
    text-align: right;
}

.admin-info span {
    display: block;
    color: #7f8c8d;
}

.admin-info span:first-child {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-info h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-info p {
    margin: 0;
    color: #7f8c8d;
    font-weight: 500;
}

/* Admin Modules */
.admin-modules {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.module-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    display: flex;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
}

.module-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
    font-size: 2rem;
    color: white;
}

.module-card:nth-child(1) .module-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.module-card:nth-child(2) .module-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.module-card:nth-child(3) .module-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.module-card:nth-child(4) .module-icon {
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    color: #2d3436;
}

.module-card:nth-child(5) .module-icon {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
}

.module-content {
    flex: 1;
}

.module-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    color: #2c3e50;
    font-weight: 700;
}

.module-content p {
    margin: 0 0 1rem 0;
    color: #7f8c8d;
    line-height: 1.6;
}

.module-stats {
    color: #3498db;
    font-weight: 600;
    font-size: 0.9rem;
}

.module-arrow {
    font-size: 1.5rem;
    color: #bdc3c7;
    transition: color 0.3s ease;
}

.module-card:hover .module-arrow {
    color: #3498db;
}

/* Recent Activities */
.recent-activities {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.recent-activities h2 {
    margin: 0 0 1.5rem 0;
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 700;
}

.recent-activities h2 i {
    color: #3498db;
    margin-right: 0.5rem;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
}

.activity-content p {
    margin: 0;
    color: #2c3e50;
    font-weight: 500;
}

.activity-time {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .admin-header h1 {
        font-size: 1.5rem;
        white-space: normal;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .module-card {
        flex-direction: column;
        text-align: center;
    }
    
    .module-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .module-arrow {
        display: none;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #3498db;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error Messages */
.success-message {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

/* Footer Logo Fix */
.footer-logo img {
    width: 40px !important;
    height: 40px !important;
    object-fit: contain;
}
