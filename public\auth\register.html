<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON></title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        
        .page-header {
            text-align: center;
            margin: 120px 0 30px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .register-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 50px;
            align-items: stretch;
        }
        
        .register-form-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .register-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
        }
        
        .form-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .form-header h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #4285F4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .register-btn {
            background: linear-gradient(to right, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
        }
        
        .rules-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 600px;
        }
        
        .rules-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #ff7aa8, #4285F4);
        }
        
        .rules-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .rules-header {
            flex-shrink: 0;
            margin-bottom: 15px;
        }

        .rules-header h2 {
            color: #ff7aa8;
            font-size: 1.8rem;
            margin-bottom: 0;
        }
        
        .rules-content {
            flex: 1;
            max-height: 900px;
            overflow-y: auto;
            padding-right: 15px;
            padding-bottom: 20px;
        }
        
        .rules-content::-webkit-scrollbar {
            width: 8px;
        }

        .rules-content::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 10px;
            margin: 5px 0;
        }

        .rules-content::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .rules-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #3367D6, #e55a8a);
            transform: scale(1.1);
        }
        
        .rules-content h2 {
            color: #4285F4;
            font-size: 1.4rem;
            margin: 0 0 15px;
            text-align: center;
        }

        .rules-content h3 {
            color: #4285F4;
            font-size: 1.1rem;
            margin: 20px 0 10px;
        }
        
        .rules-content h3:first-child {
            margin-top: 0;
        }
        
        .rules-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .rules-content ol {
            padding-left: 20px;
            margin-bottom: 15px;
        }
        
        .rules-content li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .rules-content li ol {
            list-style-type: lower-alpha;
            margin: 10px 0;
        }
        
        .contact-info {
            margin-top: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        
        .contact-info h2 {
            color: #4285F4;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .contact-methods {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        
        .contact-method {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            padding: 20px;
            border-radius: 10px;
            background-color: #f8f9fa;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .contact-method i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #ff7aa8;
        }
        
        .contact-method h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
            color: #333;
        }
        
        .contact-method p,
        .contact-method a {
            color: #666;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .contact-method a:hover {
            color: #4285F4;
        }
        
        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .agreement-checkbox input {
            margin-top: 5px;
            margin-right: 10px;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }

        .error-message {
            background-color: rgba(255, 77, 77, 0.1);
            color: #ff4d4d;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading i {
            color: #4285F4;
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .register-container {
                flex-direction: column;
            }
            
            .contact-methods {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-method {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* Hiệu ứng animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .register-form-container, .rules-container, .contact-info {
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        .rules-container {
            animation-delay: 0.2s;
        }
        
        .contact-info {
            animation-delay: 0.4s;
            opacity: 0;
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        /* Course Registration Sections */
        .course-registration-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .python-section {
            background: linear-gradient(135deg, #3776ab 0%, #ffd43b 100%);
            color: white;
        }

        .scratch-section {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }

        .stem-section {
            background: linear-gradient(135deg, #6f42c1 0%, #007bff 100%);
            color: white;
        }

        .course-intro {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 20px;
        }

        .course-content {
            z-index: 2;
        }

        .course-header {
            margin-bottom: 30px;
        }

        .course-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .course-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .course-description {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .course-features {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .feature-item i {
            font-size: 1.3rem;
            opacity: 0.8;
        }

        .register-course-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .register-course-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* Python Animation */
        .python-animation {
            position: relative;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stick-figure {
            position: relative;
            animation: walk 3s infinite ease-in-out;
        }

        .stick-figure .head {
            width: 30px;
            height: 30px;
            border: 3px solid white;
            border-radius: 50%;
            margin: 0 auto 10px;
        }

        .stick-figure .body {
            width: 3px;
            height: 60px;
            background: white;
            margin: 0 auto;
        }

        .stick-figure .arms {
            position: absolute;
            top: 40px;
            left: 50%;
            transform: translateX(-50%);
        }

        .stick-figure .arm {
            width: 25px;
            height: 3px;
            background: white;
            position: absolute;
            border-radius: 2px;
        }

        .stick-figure .arm.left {
            transform: rotate(-30deg);
            left: -20px;
        }

        .stick-figure .arm.right {
            transform: rotate(30deg);
            right: -20px;
        }

        .stick-figure .legs {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
        }

        .stick-figure .leg {
            width: 25px;
            height: 3px;
            background: white;
            position: absolute;
            border-radius: 2px;
        }

        .stick-figure .leg.left {
            transform: rotate(-20deg);
            left: -15px;
        }

        .stick-figure .leg.right {
            transform: rotate(20deg);
            right: -15px;
        }

        @keyframes walk {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(20px); }
        }

        .code-lines {
            position: absolute;
            top: 50px;
            left: 100px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .code-line {
            background: rgba(0, 0, 0, 0.7);
            color: #ffd43b;
            padding: 8px 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            opacity: 0;
            animation: fadeInCode 0.5s ease forwards;
            animation-delay: var(--delay);
            white-space: nowrap;
        }

        @keyframes fadeInCode {
            to { opacity: 1; }
        }

        .falling-text {
            position: absolute;
            top: 200px;
            left: 150px;
            color: #ffd43b;
            font-weight: bold;
            font-size: 1.2rem;
            animation: fall 2s ease-in-out infinite;
            animation-delay: 5s;
        }

        @keyframes fall {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(50px); opacity: 0; }
        }

        /* Scratch Animation */
        .scratch-animation {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
        }

        .video-placeholder {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .video-placeholder i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .scratch-blocks {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .block {
            padding: 10px 20px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            text-align: center;
            animation: slideIn 0.5s ease forwards;
            opacity: 0;
            transform: translateX(-50px);
        }

        .block.orange {
            background: #ff8c00;
            animation-delay: 0.5s;
        }

        .block.blue {
            background: #4a90e2;
            animation-delay: 1s;
        }

        .block.purple {
            background: #9b59b6;
            animation-delay: 1.5s;
        }

        .block.green {
            background: #27ae60;
            animation-delay: 2s;
        }

        @keyframes slideIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* STEM Animation */
        .stem-showcase {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
        }

        .achievement-images {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .achievement-img {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .achievement-img:hover {
            transform: scale(1.1);
        }

        .achievement-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .achievement-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px;
            font-size: 0.8rem;
            text-align: center;
        }

        .stem-icons {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .stem-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            animation: bounce 2s infinite;
            backdrop-filter: blur(10px);
        }

        .stem-icon:nth-child(1) { animation-delay: 0s; }
        .stem-icon:nth-child(2) { animation-delay: 0.5s; }
        .stem-icon:nth-child(3) { animation-delay: 1s; }
        .stem-icon:nth-child(4) { animation-delay: 1.5s; }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* Registration Modal */
        .registration-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-header h2 {
            color: #333;
            margin: 0;
            font-size: 1.8rem;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            background: #f0f0f0;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4285F4;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .cancel-btn, .submit-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #6c757d;
        }

        .cancel-btn:hover {
            background: #e9ecef;
        }

        .submit-btn {
            background: #4285F4;
            color: white;
        }

        .submit-btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
        }

        .success-message {
            text-align: center;
            padding: 30px;
        }

        .success-message i {
            font-size: 3rem;
            color: #27ae60;
            margin-bottom: 20px;
        }

        .success-message h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }

        .success-message p {
            color: #666;
            line-height: 1.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .course-intro {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .course-header h2 {
                font-size: 2rem;
            }

            .course-icon {
                font-size: 3rem;
            }

            .python-animation,
            .scratch-animation,
            .stem-showcase {
                height: 300px;
            }

            .code-lines {
                position: static;
                margin-top: 20px;
            }

            .achievement-images {
                flex-direction: column;
                align-items: center;
            }

            .achievement-img {
                width: 100px;
                height: 100px;
            }

            .modal-content {
                padding: 20px;
                margin: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html" class="active">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Course Registration Sections -->

    <!-- Python Course Section -->
    <section class="course-registration-section python-section" id="python-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fab fa-python"></i>
                        </div>
                        <h2>Python - AI từ Cơ Bản đến Nâng Cao</h2>
                        <p class="course-description">Khám phá thế giới lập trình Python và Trí tuệ nhân tạo với những dự án thực tế hấp dẫn</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh THCS - THPT</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>250,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="showRegistrationForm('python')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="python-animation">
                        <div class="stick-figure">
                            <div class="head"></div>
                            <div class="body"></div>
                            <div class="arms">
                                <div class="arm left"></div>
                                <div class="arm right"></div>
                            </div>
                            <div class="legs">
                                <div class="leg left"></div>
                                <div class="leg right"></div>
                            </div>
                        </div>
                        <div class="code-lines">
                            <div class="code-line" style="--delay: 1s;">print("Hello World!")</div>
                            <div class="code-line" style="--delay: 2s;">name = "Python"</div>
                            <div class="code-line" style="--delay: 3s;">for i in range(5):</div>
                            <div class="code-line" style="--delay: 4s;">&nbsp;&nbsp;&nbsp;&nbsp;print(f"Learning {name}!")</div>
                        </div>
                        <div class="falling-text">Hello World!</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scratch Course Section -->
    <section class="course-registration-section scratch-section" id="scratch-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <h2>Scratch - Tin Học Cơ Bản</h2>
                        <p class="course-description">Bước đầu tiên vào thế giới lập trình với Scratch - ngôn ngữ lập trình trực quan và thú vị</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh Tiểu học</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>300,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="showRegistrationForm('scratch')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="scratch-animation">
                        <!-- Placeholder for video - will be added when video is provided -->
                        <div class="video-placeholder">
                            <i class="fas fa-play-circle"></i>
                            <p>Video demo Scratch sẽ được thêm vào đây</p>
                        </div>
                        <div class="scratch-blocks">
                            <div class="block orange">when clicked</div>
                            <div class="block blue">move 10 steps</div>
                            <div class="block purple">say "Hello!" for 2 seconds</div>
                            <div class="block green">repeat 10</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- STEM Course Section -->
    <section class="course-registration-section stem-section" id="stem-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <h2>Hỗ Trợ Nghiên Cứu KHKT - STEM</h2>
                        <p class="course-description">Phát triển tư duy khoa học và kỹ thuật thông qua các dự án nghiên cứu thực tế</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh THCS - THPT</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>350,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="showRegistrationForm('stem')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="stem-showcase">
                        <div class="achievement-images">
                            <div class="achievement-img">
                                <img src="../assets/images/achievements/covid-app-award.jpg" alt="COVID App Award">
                                <div class="achievement-label">Giải Nhất Quốc gia</div>
                            </div>
                            <div class="achievement-img">
                                <img src="../assets/images/achievements/heart-disease-prediction-1.jpg" alt="Heart Disease Prediction">
                                <div class="achievement-label">Giải Nhì Tỉnh</div>
                            </div>
                            <div class="achievement-img">
                                <img src="../assets/images/achievements/forest-fire-detection-1.jpg" alt="Forest Fire Detection">
                                <div class="achievement-label">Giải Ba Tỉnh</div>
                            </div>
                        </div>
                        <div class="stem-icons">
                            <div class="stem-icon"><i class="fas fa-flask"></i></div>
                            <div class="stem-icon"><i class="fas fa-robot"></i></div>
                            <div class="stem-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="stem-icon"><i class="fas fa-brain"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Form Modal -->
    <div class="registration-modal" id="registrationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Đăng Ký Khóa Học</h2>
                <button class="close-modal" onclick="hideRegistrationForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="registrationForm">
                <input type="hidden" id="selectedCourse" name="course">

                <div class="form-group">
                    <label for="studentName">Tên Học Viên <span class="required">*</span></label>
                    <input type="text" id="studentName" name="studentName" required>
                </div>

                <div class="form-group">
                    <label for="birthDate">Ngày Tháng Năm Sinh <span class="required">*</span></label>
                    <input type="date" id="birthDate" name="birthDate" required>
                </div>

                <div class="form-group">
                    <label for="parentName">Tên Phụ Huynh <span class="required">*</span></label>
                    <input type="text" id="parentName" name="parentName" required>
                </div>

                <div class="form-group">
                    <label for="parentPhone">Số Điện Thoại Phụ Huynh <span class="required">*</span></label>
                    <input type="tel" id="parentPhone" name="parentPhone" required>
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="hideRegistrationForm()">Hủy</button>
                    <button type="submit" class="submit-btn">Gửi Đăng Ký</button>
                </div>
            </form>

            <div class="success-message" id="successMessage" style="display:none;">
                <i class="fas fa-check-circle"></i>
                <h3>Đăng ký thành công!</h3>
                <p>Chúng tôi sẽ liên hệ với bạn sớm để xác nhận và cung cấp thông tin chi tiết.</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getFirestore, collection, addDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Course titles mapping
        const courseTitles = {
            'python': 'Python - AI từ Cơ Bản đến Nâng Cao',
            'scratch': 'Scratch - Tin Học Cơ Bản',
            'stem': 'Hỗ Trợ Nghiên Cứu KHKT - STEM'
        };

        // Show registration form modal
        window.showRegistrationForm = function(courseType) {
            const modal = document.getElementById('registrationModal');
            const modalTitle = document.getElementById('modalTitle');
            const selectedCourse = document.getElementById('selectedCourse');

            modalTitle.textContent = `Đăng Ký - ${courseTitles[courseType]}`;
            selectedCourse.value = courseType;
            modal.style.display = 'block';

            // Reset form and hide messages
            document.getElementById('registrationForm').reset();
            document.getElementById('successMessage').style.display = 'none';
        };

        // Hide registration form modal
        window.hideRegistrationForm = function() {
            const modal = document.getElementById('registrationModal');
            modal.style.display = 'none';
        };

        // Close modal when clicking outside
        document.getElementById('registrationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideRegistrationForm();
            }
        });

        // Handle form submission
        const form = document.getElementById('registrationForm');
        const successMessage = document.getElementById('successMessage');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('studentName').value,
                    birthDate: document.getElementById('birthDate').value,
                    parentName: document.getElementById('parentName').value,
                    parentPhone: document.getElementById('parentPhone').value,
                    course: document.getElementById('selectedCourse').value,
                    courseName: courseTitles[document.getElementById('selectedCourse').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending' // pending, approved, rejected
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Hide form and show success message
                form.style.display = 'none';
                successMessage.style.display = 'block';

                // Auto close modal after 3 seconds
                setTimeout(() => {
                    hideRegistrationForm();
                    form.style.display = 'block';
                    successMessage.style.display = 'none';
                    form.reset();
                }, 3000);

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Smooth scrolling for course sections
        function scrollToCourse(courseId) {
            document.getElementById(courseId + '-registration').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Check URL parameters for direct course access
        const urlParams = new URLSearchParams(window.location.search);
        const course = urlParams.get('course');
        if (course && courseTitles[course]) {
            setTimeout(() => {
                scrollToCourse(course);
            }, 500);
        }
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html> 