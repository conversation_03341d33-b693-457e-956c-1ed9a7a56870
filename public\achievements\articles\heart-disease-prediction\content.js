// Heart Disease Prediction Achievement Content
export const heartDiseasePredictionContent = {
    id: "heart-disease-prediction",
    title: "🏅 Đạt giải <PERSON>hì cuộc thi <PERSON><PERSON> học <PERSON> thuật cấp Tỉnh – Năm học 2024–2025",
    date: "20/05/2025",
    excerpt: "Hệ thống hỗ trợ dự đoán bệnh tim thông qua điện tâm đồ và mô hình ngôn ngữ lớn đạt giải Nhì cấp tỉnh Kon Tum.",
    images: [
        "../assets/images/achievements/heart-disease-prediction-1.jpg",
        "../assets/images/achievements/heart-disease-prediction-2.jpg",
        "../assets/images/achievements/heart-disease-prediction-3.jpg"
    ],
    content: `
        <div class="project-info-section">
            <h2>📋 Thông tin dự án</h2>
            <div class="info-grid">
                <div class="info-item"><strong>👤 Họ và tên:</strong> <PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON>ch Tiến <PERSON>t</div>
                <div class="info-item"><strong>🏫 Trường:</strong> THPT Chuyên <PERSON>ễ<PERSON> Tất Thành</div>
                <div class="info-item"><strong>📍 Tỉnh/Thành phố:</strong> Kon Tum</div>
                <div class="info-item"><strong>📅 Thời gian:</strong> Năm học 2024–2025</div>
                <div class="info-item"><strong>📘 Lĩnh vực:</strong> Hệ thống nhúng (Embedded Systems)</div>
                <div class="info-item"><strong>📱 Tên dự án:</strong> Hệ thống hỗ trợ dự đoán bệnh tim thông qua điện tâm đồ và mô hình ngôn ngữ lớn</div>
                <div class="info-item"><strong>🥈 Giải thưởng:</strong> Giải Nhì – Cấp Tỉnh</div>
            </div>
        </div>

        <h2>🏆 Mô tả thành tích</h2>
        <p>Thành tích đạt được tại Cuộc thi Khoa học Kỹ thuật cấp Tỉnh gắn với các hoạt động STEM dành cho học sinh trung học năm học 2024–2025, do Sở Giáo dục và Đào tạo tỉnh Kon Tum tổ chức. Dự án được đánh giá cao về tính ứng dụng, khả năng tích hợp công nghệ hiện đại và tư duy sáng tạo.</p>

        <h2>💡 Giới thiệu dự án</h2>
        <p>Dự án sử dụng công nghệ Hệ thống nhúng để thu thập và xử lý dữ liệu điện tâm đồ (ECG) của bệnh nhân, sau đó kết hợp với mô hình ngôn ngữ lớn (LLM) để phân tích và dự đoán nguy cơ mắc bệnh tim.</p>

        <div class="feature-list">
            <div class="feature-item">
                <strong>📊 Phân tích dữ liệu điện tâm đồ:</strong> theo thời gian thực thông qua cảm biến và mạch xử lý nhúng.
            </div>
            <div class="feature-item">
                <strong>🤖 Mô hình ngôn ngữ lớn (LLM):</strong> đóng vai trò tổng hợp dữ liệu bệnh sử, triệu chứng, lịch sử đo để đưa ra chẩn đoán sơ bộ và cảnh báo.
            </div>
            <div class="feature-item">
                <strong>🧠 Hệ thống học thích nghi:</strong> có khả năng học và thích nghi, giúp tăng độ chính xác theo thời gian sử dụng.
            </div>
        </div>

        <h2>🎯 Mục tiêu & Ý nghĩa</h2>
        <ul>
            <li>Hỗ trợ chẩn đoán sớm các bệnh lý tim mạch, đặc biệt trong cộng đồng thiếu thiết bị y tế hiện đại.</li>
            <li>Tạo nền tảng công nghệ để phổ cập hệ thống hỗ trợ y tế từ xa, giảm tải cho các cơ sở y tế tuyến đầu.</li>
            <li>Tích hợp AI vào các thiết bị nhúng nhằm nâng cao hiệu suất xử lý và ứng dụng thực tiễn.</li>
        </ul>

        <div class="certificate-info">
            <h3>📅 Ngày cấp giấy chứng nhận: 20/05/2025</h3>
        </div>
    `
};
